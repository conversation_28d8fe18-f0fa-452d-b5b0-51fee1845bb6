import { useContext, useEffect, useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../app/hooks";
import gridIconHover from "../../assets/images/companiesView/gridIconHover.png";
import gridView from "../../assets/images/companiesView/gridviewIcon.png";
import listIconHover from "../../assets/images/companiesView/listIconHover.png";
import listView from "../../assets/images/companiesView/listviewIcon.png";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import { LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW } from "../../constants/local-storage-constants";
import { CompanyContext } from "../companies/CompanyContext";
import { onEmployeesLoaded } from "./employeesActions";
import EmployeesGridView from "./EmployeesGridView";
import EmployeesListView from "./EmployeesListView";

const EmployeesContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ImageListView = styled(Container)<{ isListView: boolean }>`
  position: absolute;
  background-size: cover;
  height: 2.5rem;
  width: 2.5rem;
  right: 4rem;
  top: 6rem;
  transform: translateY(-50%);
  background-image: url(${listView});

  &:hover {
    background-image: url(${listIconHover});
  }
`;

const ImageGridView = styled(Container)<{ isListView: boolean }>`
  position: absolute;
  background-size: cover;
  height: 2rem;
  width: 2rem;
  right: 1.5rem;
  top: 6rem;
  transform: translateY(-50%);
  background-image: url(${gridView});

  &:hover {
    background-image: url(${gridIconHover});
  }
`;

const NewEmployeeButton = styled(Button)`
  margin: 0;
  margin-bottom: 0.5rem;
  margin-right: 4rem;
  width: 12rem;
  align-self: flex-end;
`;

const Employees = () => {
  const { company } = useContext(CompanyContext);
  const dispatch = useAppDispatch();
  const [isListView, setIsListView] = useState<boolean>(
    localStorage.getItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW) !== "false"
  );

  const handleClickListView = () => {
    localStorage.setItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW, `${true}`);

    setIsListView(true);
  };

  const handleClickGridView = () => {
    localStorage.setItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW, `${false}`);

    setIsListView(false);
  };

  useEffect(() => {
    if (company && company.id) {
      dispatch(onEmployeesLoaded(company.id));
    }
  }, [dispatch, company]);

  return (
    <EmployeesContainer data-testid="employees-container">
      <NewEmployeeButton
        label="New Employee"
        data-testid="add-employee-button"
      />
      <ImageListView
        data-testid="employees-list-view-button"
        isListView={isListView}
        onClick={handleClickListView}
      />
      <ImageGridView
        data-testid="employees-grid-view-button"
        isListView={!isListView}
        onClick={handleClickGridView}
      />
      {isListView ? (
        <EmployeesListView data-testid="employees-list-view" />
      ) : (
        <EmployeesGridView data-testid="employees-grid-view" />
      )}
    </EmployeesContainer>
  );
};
export default Employees;
