import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { onTRZEventsLoaded, onTRZEventImported, selectTRZEvents, onAbsenceTypesLoaded, onHospitalTypesLoaded } from "./eventsActions";
import Button from "../../components/Inputs/Button";
import styled from "styled-components";
import { TRZEvents } from "../../models/DTOs/trz/TRZEventsDTO";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";

const TableWrapper = styled.table`
  border-collapse: collapse;
  margin-top: 1.25rem;
`;

const Wrapper = styled.div`
  display: table;
  margin-left: auto;
  margin-right: auto;
`;

const TableRow = styled.tr`
  background-color: var(--table-row-color);
`;

const TableHeaderCell = styled.th`
  padding: 0.75rem 0 0.75rem 0;
  text-align: center;
  background-color: var(--table-row-color);
  color: var(--table-header-color);
  padding: 0.7rem;
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  &:first-child {
    border-radius: 1.625rem 0 0 0;
  }
  &:last-child {
    border-radius: 0 1.625rem 0 0;
  }
`;

const TableRowItem = styled.tr`
  &:first-child {
    width: 2.5rem;
  }
  border-bottom: 1px solid var(--table-row-bottom-line-color);
    &:first-child {
    border-radius: 1.625rem 0 0 0;
  }
`;

const TableCell = styled.td`
  padding: 0.75rem;
  font-size: 0.875rem;
  color: var(--table-cell-color);
  background-color: var(--table-cell-backgroundColor);
  text-align: center;
`;

const Events = () => {
    const dispatch = useAppDispatch();
    const events = useAppSelector(selectTRZEvents).trzEvents;
    const [trzEvents, setTrzEvents] = useState(events);
    const absenceTypes = useAppSelector(selectTRZEvents).absenceTypes;
    const hospitalTypes = useAppSelector(selectTRZEvents).hospitalTypes;
    const payrolls = useAppSelector(selectPayrolls).payrolls;

    useEffect(() => {
        //TO DO
        dispatch(onTRZEventsLoaded("60156213-abcd-4b94-8c48-89158ec15ae7"));
        //
        dispatch(onAbsenceTypesLoaded());
        dispatch(onHospitalTypesLoaded());
        const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
        dispatch(onPayrollsLoaded(companyId));
    }, [dispatch]);

    useEffect(() => {
        setTrzEvents(events);
    }, [events, trzEvents]);

    const handleImportEvent = async (event: TRZEvents) => {
        dispatch(onTRZEventImported(event));
    };

    return (
      <Wrapper data-testid="events-wrapper">
        <TableWrapper data-testid="events-table">
          <thead>
            <TableRow data-testid="events-header-row">
              <TableHeaderCell data-testid="events-header-payroll">
                Payroll
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-event-type">
                Event Type
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-start-date">
                Start Date
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-end-date">
                End Date
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-note">
                Note
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-sick-note">
                Sick Note
              </TableHeaderCell>
              <TableHeaderCell data-testid="events-header-actions"></TableHeaderCell>
            </TableRow>
          </thead>
          <tbody>
            {trzEvents?.map((event) => (
              <>
                <TableRowItem
                  key={event.id}
                  data-testid={`events-row-${event.id}`}
                >
                  <TableCell data-testid={`events-cell-payroll-${event.id}`}>
                    {
                      payrolls.find((p) => p.id === event.workTimePayrollId)
                        ?.employee.firstName
                    }
                  </TableCell>
                  <TableCell data-testid={`events-cell-event-type-${event.id}`}>
                    {event.isHospital
                      ? hospitalTypes.find(
                          (e) => e.identifier === event.eventType
                        )?.name
                      : absenceTypes.find(
                          (e) => e.identifier === event.eventType
                        )?.name}
                  </TableCell>
                  <TableCell data-testid={`events-cell-start-date-${event.id}`}>
                    {new Date(event.startDate).toLocaleDateString("bg-BG")}
                  </TableCell>
                  <TableCell data-testid={`events-cell-end-date-${event.id}`}>
                    {new Date(event.endDate).toLocaleDateString("bg-BG")}
                  </TableCell>
                  <TableCell data-testid={`events-cell-note-${event.id}`}>
                    {event.note}
                  </TableCell>
                  <TableCell data-testid={`events-cell-sick-note-${event.id}`}>
                    {event.sickNote}
                  </TableCell>
                  <TableCell data-testid={`events-cell-actions-${event.id}`}>
                    <Button
                      data-testid={`events-import-button-${event.id}`}
                      label="Import"
                      onClick={() => handleImportEvent(event)}
                    />
                  </TableCell>
                </TableRowItem>
              </>
            ))}
          </tbody>
        </TableWrapper>
      </Wrapper>
    );
};
export default Events;