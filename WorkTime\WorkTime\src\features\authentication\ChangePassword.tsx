import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import MainWindowContainer from "../../components/MainWindowContainer";
import { ChangeEvent, MouseEvent, useContext, useState } from "react";
import { AuthContext } from "./AuthContext";
import EmailBox from "../../components/Inputs/EmailBox";
import PasswordBox from "../../components/Inputs/PasswordBox";
import Button from "../../components/Inputs/Button";
import { PasswortStrengthType } from "../../models/Enums/PasswortStrengthType";
import styled from "styled-components";
import Container from "../../components/Container";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";
import { authenticatedPost } from "../../services/connectionService";
import { LOCAL_STORAGE_HAS_SIGNED_IN } from "../../constants/local-storage-constants";
import { ChangePasswordRequest } from "../../models/Requests/ChangePasswordRequest";

const CreateEmployeeContainer = styled(MainWindowContainer)`
  height: 100%;
  margin: 0 auto;
  width: clamp(40%, 30rem, 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const PasswordStrengthContainer = styled(Container)`
  width: 100%;
  display: flex;
  padding: 0 1rem 0 1rem;
  box-sizing: border-box;
`;

const PasswordStrengthRectangle = styled(Container)<{
  $passwordStrength: PasswortStrengthType;
}>`
  background-color: var(--password-strength-rectangle-empty-background-color);
  width: 100%;
  height: 0.4rem;
  margin: 0.5rem 0.5rem 0.25rem 0.5rem;
`;

const PasswordStrengthRectangleOne = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength === PasswortStrengthType.Weak &&
    "var(--password-strength-rectangle-weak-background-color)"};
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Middle &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleTwo = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Middle &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleThree = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Good &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleFour = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Strong &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;

const ChangePassword = () => {
  const [searchParams] = useSearchParams();
  const { user, setUser } = useContext(AuthContext);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [enabled, setEnabled] = useState(false);
  const navigate = useNavigate();
  const [passwordStrength, setPasswordStrength] = useState(
    PasswortStrengthType.Empty
  );

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPassword(e.currentTarget.value);

    const currentPasswordStrength = passwordStrengthCheck(
      e.currentTarget.value
    );
    setPasswordStrength(currentPasswordStrength);

    setEnabled(
      e.currentTarget.value === confirmPassword &&
        e.currentTarget.value !== "" &&
        currentPasswordStrength > 2
    );
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.currentTarget.value);

    setEnabled(
      e.currentTarget.value === password &&
        e.currentTarget.value !== "" &&
        passwordStrength > 2
    );
  };

  const handleChangePasswordClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const request = {
      password: password,
    } as ChangePasswordRequest;

    authenticatedPost("sso/change-password", request).then(() => {
      localStorage.setItem(LOCAL_STORAGE_HAS_SIGNED_IN, "true");
      setUser({ ...user, hasSignedIn: true });
      navigate(searchParams.get("returnAfterPasswordChanged") ?? "/");
    });
  };

  return (
    <CreateEmployeeContainer data-testid="create-employee-container">
      <PasswordBox
        name="password"
        handleChange={handlePasswordChange}
        label="Password"
        type="password"
        value={password}
        data-testid="password-box"
      />
      <PasswordBox
        name="confirm-password"
        handleChange={handleConfirmPasswordChange}
        label="Confirm Password"
        type="password"
        value={confirmPassword}
        data-testid="confirm-password-box"
      />
      <PasswordStrengthContainer data-testid="password-strength-container">
        <PasswordStrengthRectangleOne
          data-testid="password-strength-rectangle-one"
          $passwordStrength={passwordStrength}
        />
        <PasswordStrengthRectangleTwo
          data-testid="password-strength-rectangle-two"
          $passwordStrength={passwordStrength}
        />
        <PasswordStrengthRectangleThree
          data-testid="password-strength-rectangle-three"
          $passwordStrength={passwordStrength}
        />
        <PasswordStrengthRectangleFour
          data-testid="password-strength-rectangle-four"
          $passwordStrength={passwordStrength}
        />
      </PasswordStrengthContainer>
      <Button
        label="Change password"
        disabled={!enabled}
        onClick={handleChangePasswordClick}
        data-testid="change-password-button"
      />
    </CreateEmployeeContainer>
  );
};

export default ChangePassword;
