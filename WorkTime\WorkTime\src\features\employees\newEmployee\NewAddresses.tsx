import { useEffect, useState } from "react";
import styled from "styled-components";
import infoIcon from "../../../assets/images/icons/info.svg";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import profileAvatar from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1rem;
`;

const ProfileSection = styled(Container)`
  display: flex;
  flex-direction: row;
  margin-bottom: 2rem;
`;

const AvatarContainer = styled(Container)`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  margin-right: 2rem;
`;

const ProfileAvatar = styled(Image)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const PersonalInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionIcon = styled(Image)`
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: #333;
`;

const FormRow = styled(Container)`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormField = styled(Container)`
  flex: 1;
`;

const FullWidthField = styled(Container)`
  width: 100%;
  margin-bottom: 1rem;
`;

const CopyButton = styled(Button)`
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  background-image: url(${copyIcon});
  background-size: contain;
  background-repeat: no-repeat;
`;

const CopyContainer = styled(Container)`
  display: flex;
  align-items: center;
`;

const CopyLabel = styled(Label)`
  margin-right: 0.5rem;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: any;
}

const NewAddresses = ({ onValidation, data = {} }: Props) => {
  const [formData, setFormData] = useState({
    city: data.city || "",
    district: data.district || "",
    municipality: data.municipality || "",
    postalCode: data.postalCode || "",
    street: data.street || "",
    block: data.block || "",
    apartment: data.apartment || "",
    neighborhood: data.neighborhood || "",
    phone: data.phone || "",
    workPhone: data.workPhone || "",
    email: data.email || "",
    workEmail: data.workEmail || "",
    ...data,
  });

  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    // Basic validation - check if required fields are filled
    const valid = !!formData.city && !!formData.street && !!formData.email;
    setIsValid(valid);

    if (onValidation) {
      onValidation(valid, formData);
    }
  }, [formData, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    //const { name, value } = e.target;
    // setFormData((prev) => ({
    //   ...prev,
    //   [name]: value,
    // }));
  };

  return (
    <FormContainer>
      <ProfileSection>
        <AvatarContainer>
          <ProfileAvatar src={profileAvatar} alt="Profile" />
        </AvatarContainer>
        <PersonalInfoContainer>
          <Textbox
            name="firstName"
            label="Име"
            value={formData.firstName || ""}
            handleChange={handleChange}
          />
          <Textbox
            name="middleName"
            label="Презиме"
            value={formData.middleName || ""}
            handleChange={handleChange}
          />
          <Textbox
            name="lastName"
            label="Фамилия"
            value={formData.lastName || ""}
            handleChange={handleChange}
          />
        </PersonalInfoContainer>
      </ProfileSection>

      <SectionHeader>
        <SectionIcon src={infoIcon} alt="Basic Info" />
        <SectionTitle>Основни данни</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="egn"
            label="ЕГН / ЛНЧ"
            value={formData.egn || ""}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="birthDate"
            label="Дата на раждане"
            value={formData.birthDate || ""}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="contractType"
            label="Тип договор"
            value={formData.contractType || ""}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="birthPlace"
            label="Място на раждане"
            value={formData.birthPlace || ""}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FullWidthField>
        <Textbox
          name="iban"
          label="IBAN"
          value={formData.iban || ""}
          handleChange={handleChange}
        />
      </FullWidthField>

      <SectionHeader>
        <SectionIcon src={infoIcon} alt="Profile Info" />
        <SectionTitle>Данни за създаване на профил</SectionTitle>
      </SectionHeader>

      <FullWidthField>
        <Textbox
          name="email"
          label="E-mail"
          value={formData.email || ""}
          handleChange={handleChange}
        />
      </FullWidthField>

      <CopyContainer>
        <CopyLabel>Служебен №</CopyLabel>
        <Label>12345678</Label>
      </CopyContainer>
    </FormContainer>
  );
};

export default NewAddresses;
