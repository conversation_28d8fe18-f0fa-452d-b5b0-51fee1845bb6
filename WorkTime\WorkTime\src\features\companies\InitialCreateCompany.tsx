import { ChangeEvent, MouseEvent, useEffect, useState } from "react";
import Form from "../../components/Form/Form";
import Textbox from "../../components/Inputs/Textbox";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import Button from "../../components/Inputs/Button";
import { useAppDispatch } from "../../app/hooks";
import { onCompanyCreated, onCompanyImported } from "./companiesActions";
import MainWindowContainer from "../../components/MainWindowContainer";
import styled from "styled-components";
import Container from "../../components/Container";
import { authenticatedPost } from "../../services/connectionService";
import { useNavigate } from "react-router-dom";

const StyledButton = styled(Button)`
  width: -webkit-fill-available;
`;

const CompanyInfoContainer = styled(Container)`
  width: 100%;
  float: right;
  box-sizing: border-box;
`;

const NameEikContainer = styled(Container)`
  display: grid;
  grid-template-columns: 66% 33%;
  gap: 1%;
  width: 100%;
`;

const CustomTextbox = styled(Textbox)`
  margin-bottom: 0.5rem;
`;

interface Props {
  importCompany?: CompanyDTO;
}

const InitialCreateCompany = (CompanyProp: Props): JSX.Element => {
  const dispatch = useAppDispatch();

  const { importCompany } = CompanyProp;
  const [company, setCompany] = useState({} as CompanyDTO);
  const [isdisabled, setIsDisabled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (importCompany) {
      setCompany(importCompany as CompanyDTO);
      setIsDisabled(
        importCompany?.name?.length > 0 &&
          importCompany?.contactName?.length > 0 &&
          importCompany?.bulstat?.length > 0
      );
    }
  }, [importCompany]);

  const handleCompanyChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newCompany = {
      ...company,
      [e.currentTarget.name]: e.currentTarget.value,
    };
    setCompany(newCompany);

    setIsDisabled(
      newCompany?.name?.length > 0 &&
        newCompany?.contactName?.length > 0 &&
        newCompany?.bulstat?.length > 0
    );
  };

  const handleCreateCompany = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (Object.keys(importCompany || {}).length !== 0) {
      authenticatedPost<CompanyDTO>("add-employee-company", importCompany)
        .then(() => {
          dispatch(onCompanyImported(importCompany!));
          navigate("/");
        })
        .catch((error) => console.error(error));
    } else {
      authenticatedPost<CompanyDTO>(`company`, company).then((company) => {
        dispatch(onCompanyCreated(company));
        navigate("/");
      });
    }
  };

  return (
    <MainWindowContainer>
      <Form>
        <CompanyInfoContainer>
          <NameEikContainer>
            <CustomTextbox
              name="name"
              handleChange={handleCompanyChange}
              value={company.name}
              label="Company Name"
              data-testid="company-name"
            />
            <CustomTextbox
              name="bulstat"
              handleChange={handleCompanyChange}
              value={company.bulstat}
              label="EIK"
              data-testid="company-bulstat"
            />
          </NameEikContainer>
          <CustomTextbox
            name="contactName"
            handleChange={handleCompanyChange}
            value={company.contactName}
            label="MOL"
            data-testid="company-contact-name"
          />
          <CustomTextbox
            name="MOL"
            handleChange={handleCompanyChange}
            value={company.MOL}
            label="MOL"
            data-testid="company-mol"
          />
        </CompanyInfoContainer>
        <StyledButton
          label="Create Company"
          onClick={handleCreateCompany}
          disabled={!isdisabled}
          data-testid="create-company-button"
        />
      </Form>
    </MainWindowContainer>
  );
};

export default InitialCreateCompany;
