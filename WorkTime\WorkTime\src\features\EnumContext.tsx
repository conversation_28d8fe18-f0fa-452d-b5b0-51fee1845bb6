import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import {
  initContractReasons,
  initContracts,
  initTerminationReason,
  initIncomeTypes,
} from "../services/nomenclatures/contractsService";
import {
  initTypesOfAppointment,
  initPayrollCategories,
} from "../services/nomenclatures/nomenclatureService";
import { initCountries } from "../services/nomenclatures/defaultLocationDataService";
import { IEnumType } from "../models/DTOs/enums/IEnumType";

interface IEnumContext {
  contractTypes: IEnumType[];
  contractReasons: IEnumType[];
  terminationReason: IEnumType[];
  countries: IEnumType[];
  incomeTypes: IEnumType[];
  appointmentTypes: IEnumType[];
    payrollCategories: IEnumType[];
}

interface EnumProviderProps {
  children: ReactNode;
}

const EnumContext = createContext<IEnumContext>({
  contractTypes: [],
  contractReasons: [],
  terminationReason: [],
  countries: [],
  incomeTypes: [],
  appointmentTypes: [],
    payrollCategories: [],
});

export const EnumProvider: React.FC<EnumProviderProps> = ({ children }) => {
  const [enums, setEnums] = useState<IEnumContext>({
    contractTypes: [],
    contractReasons: [],
    terminationReason: [],
    countries: [],
    incomeTypes: [],
    appointmentTypes: [],
      payrollCategories: [],
    //more enums
  });

  useEffect(() => {
    initContracts().then((contractTypes) => {
      setEnums((enums) => ({ ...enums, contractTypes }));
    });
    initContractReasons().then((contractReasons) => {
      setEnums((enums) => ({ ...enums, contractReasons }));
    });
    initTerminationReason().then((terminationReason) => {
      setEnums((enums) => ({ ...enums, terminationReason }));
    });
    initCountries().then((countries) => {
      setEnums((enums) => ({ ...enums, countries }));
    });
    initIncomeTypes().then((incomeTypes) => {
      setEnums((enums) => ({ ...enums, incomeTypes }));
    });
    initTypesOfAppointment().then((appointmentTypes) => {
      setEnums((enums) => ({ ...enums, appointmentTypes }));
    });
      initPayrollCategories().then((payrollCategories) => {
          setEnums((enums) => ({ ...enums, payrollCategories }));
    });
  }, []);

  return <EnumContext.Provider value={enums}>{children}</EnumContext.Provider>;
};

export const useEnums = () => useContext(EnumContext);
