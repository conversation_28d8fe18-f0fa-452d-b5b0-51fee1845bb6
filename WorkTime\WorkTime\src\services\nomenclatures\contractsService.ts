import { IEnumType } from "../../models/DTOs/enums/IEnumType";
import { authenticatedGet } from "../connectionService";

export const initContracts = async (): Promise<IEnumType[]> => {
  try {
    const response = await authenticatedGet<IEnumType[]>(`contracts`);
    return response;
  } catch (error) {
    console.error("Error fetching contracts:", error);
    return [];
  }
};

export const initContractReasons = async (): Promise<IEnumType[]> => {
  try {
    const response = await authenticatedGet<IEnumType[]>(`contractReasons`);
    return response;
  } catch (error) {
    console.error("Error fetching contract reasons:", error);
    return [];
  }
};

export const initTerminationReason = async (): Promise<IEnumType[]> => {
  try {
    const responce = await authenticatedGet<IEnumType[]>(`termination-reason`);
    return responce;
  } catch (error) {
    console.error("Error fetching termination reasons:", error);
    return [];
  }
};

export const initIncomeTypes = async (): Promise<IEnumType[]> => {
  try {
    const responce = await authenticatedGet<IEnumType[]>(`income-types`);
    return responce;
  } catch (error) {
    console.log("Error fetching income types:", error);
    return [];
  }
};
