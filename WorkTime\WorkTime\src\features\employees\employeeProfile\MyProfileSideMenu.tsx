import styled from "styled-components";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import {
  ChangeEvent,
  useEffect,
  useState,
  MouseEvent,
  useContext,
} from "react";
import UploadButton from "../../../components/UploadButton/UploadButton";
import Textbox from "../../../components/Inputs/Textbox";
import MainWindowContainer from "../../../components/MainWindowContainer";
import Translator from "../../../services/language/Translator";
import sendEmail from "../../../assets/images/sendEmail.png";
import attentionImg from "../../../assets/images/attention.png";
import copyImg from "../../../assets/images/button/copy.png";
import copyHoverImg from "../../../assets/images/button/copyHover.png";
import { AuthContext } from "../../authentication/AuthContext";
import { authenticatedPost } from "../../../services/connectionService";
import { EditUserDataRequest } from "../../../models/Requests/EditUserDataRequest";
import { SenderaUserDTO } from "../../../models/DTOs/SenderaUserDTO";
import { getSenderaUser } from "../../../services/authentication/authenticationService";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const LogoContainer = styled(Container)`
  width: 8rem;
  float: left;
  background-color: var(--upload-button-background-color);
  height: 8rem;
  border-radius: 4.5rem;
  margin-top: 1.5rem;
`;

const MyProfileInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 65%;
  margin-bottom: 2rem;
`;

const StyledButton = styled(Button)`
  width: -webkit-fill-available;
  position: fixed;
  bottom: 0; 
  margin: 0.625rem; 
`;

const ReadOnlyTextbox = styled(Textbox)`
   background-color: var(--textbox-background-color-read-only);
`;

const SuccessMessageContainer = styled(Container)`
    text-align: center;
    margin: 3.5rem;
    font-size: 1.125rem;
    display: flex;
    flex-direction: column;
    align-items: center;
`;

const EnvelopeImage = styled.img`
  width: 6.5rem;
  height: 3.5rem;
  margin-bottom: 0.6rem;
`;

const MyProfileSideMenu = () => {
    const [newProfileData, setNewProfileData] = useState({} as SenderaUserDTO);
    const [initialEmail, setInitialEmail] = useState<string | undefined>(undefined);
    const [isReadOnly, setIsReadOnly] = useState(true);
    const [successMessageVisible, setSuccessMessageVisible] = useState(false);
    const { user } = useContext(AuthContext);

    const getSenderaUserData = async () => {
        try {
            await getSenderaUser().then((response: SenderaUserDTO) => {
                setNewProfileData(response);
                setInitialEmail(response.email);
            });
        } catch (error) { }
    };

    useEffect(() => {
      getSenderaUserData();
    }, [user]);

    useEffect(() => {
        setIsReadOnly(!!initialEmail && newProfileData?.email === initialEmail);
    }, [newProfileData?.email, initialEmail]);


    const handleEditNewProfileData = (e: MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();

        const request = {
            userId: newProfileData.id,
            firstName: newProfileData.firstName,
            secondName: newProfileData.secondName,
            lastName: newProfileData.lastName,
            email: newProfileData.email
        } as EditUserDataRequest;

        try {
            authenticatedPost("user-registration/edit-user-data", request)
                .then(() => {
                    setSuccessMessageVisible(true);
                })
                .catch((error) => {
                    console.error('Request failed:', error);
                });
        } catch (error) {
            console.log('Caught error in handler:', error);
        }
    };

    const handleNewProfileDataChange = (e: ChangeEvent<HTMLInputElement>) => {
        setNewProfileData({
            ...newProfileData,
            [e.currentTarget.name]: e.currentTarget.value,
        });
    };

    return (
      <MainContainer data-testid="profile-main-container">
        <LogoContainer data-testid="profile-logo-container">
          <UploadButton data-testid="profile-upload-button" />
        </LogoContainer>

        <MyProfileInfoContainer data-testid="profile-info-container">
          <Textbox
            data-testid="profile-firstname-input"
            name="firstName"
            label="First Name"
            value={newProfileData?.firstName}
            handleChange={handleNewProfileDataChange}
            showImage={successMessageVisible}
            imageSrc={attentionImg}
          />
          <Textbox
            data-testid="profile-secondname-input"
            name="secondName"
            label="Second name"
            value={newProfileData?.secondName}
            handleChange={handleNewProfileDataChange}
            showImage={successMessageVisible}
            imageSrc={attentionImg}
          />
          <Textbox
            data-testid="profile-lastname-input"
            name="lastName"
            label="Last name"
            value={newProfileData?.lastName}
            handleChange={handleNewProfileDataChange}
            showImage={successMessageVisible}
            imageSrc={attentionImg}
          />
        </MyProfileInfoContainer>

        <Textbox
          data-testid="profile-email-input"
          name="email"
          label="Email"
          value={newProfileData?.email}
          handleChange={handleNewProfileDataChange}
          readOnly={isReadOnly}
          showImage={successMessageVisible}
          imageSrc={attentionImg}
        />
        <ReadOnlyTextbox
          data-testid="profile-employee-number-input"
          label="Employee number"
          value="12345678"
          handleChange={handleNewProfileDataChange}
          showImage={true}
          imageSrc={copyImg}
          imageHoverSrc={copyHoverImg}
          readOnly={true}
          textToCopy="12345678"
        />
        {successMessageVisible && (
          <SuccessMessageContainer data-testid="profile-success-message">
            <EnvelopeImage
              src={sendEmail}
              alt="Envelope"
              data-testid="profile-envelope-image"
            />
            <Translator
              getString="SuccesfullSend"
              data-testid="profile-success-text"
            />
          </SuccessMessageContainer>
        )}

        <StyledButton
          data-testid="profile-edit-button"
          label="Edit"
          onClick={handleEditNewProfileData}
          disabled={successMessageVisible}
        />
      </MainContainer>
    );
}

export default MyProfileSideMenu;
