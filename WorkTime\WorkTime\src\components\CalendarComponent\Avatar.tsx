import styled from "styled-components";

interface AvatarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  photo: string;
  name: string;
  size: number;
  isVisible: boolean;
  background: string;
}

const getStringHashCode = (name: string) => {
  let initials: string;
  const nameSplit = name.split(" ");
  const nameLength = nameSplit.length;
  if (nameLength > 1) {
    initials =
      nameSplit[0].substring(0, 1) + nameSplit[nameLength - 1].substring(0, 1);
  } else if (nameLength === 1) {
    initials = nameSplit[0].substring(0, 1);
  } else return;

  return initials.toUpperCase();
};

const AvatarDiv = styled.div<{
  initials: string;
  background: string;
  isVisible: boolean;
}>`
  display: ${(props) => (props.isVisible ? "block" : "none")};
  position: relative;
  overflow: hidden;
  color: var(--avatar-initials-color);
  text-align: center;
  background: ${(p) => p.background};
  border-radius: 50%;
  height: 100%;
  width: 1.6em;
`;

const AvatarPhoto = styled.img<{ size: number }>`
  ${({ size }) => `
       height: ${size}rem;
       width: ${size}rem;
  `}
  border-radius: 1rem;
  margin-left: 1rem;
`;

const Avatar = (props: AvatarProps) => {
  const { photo, name, size, isVisible, background } = props;
  const initials = getStringHashCode(name) ?? "";
  return (
    <>
      {photo.length === 0 ? (
        <AvatarDiv
          background={background}
          initials={initials}
          isVisible={isVisible}
          data-testid="avatar-initials"
        >
          {photo} {initials}
        </AvatarDiv>
      ) : (
        <AvatarPhoto src={photo} size={size} data-testid="avatar-photo" />
      )}
    </>
  );
};

export default Avatar;
