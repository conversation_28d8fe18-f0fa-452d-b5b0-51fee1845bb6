import { useContext } from "react";
import { Route, Routes } from "react-router-dom";
import { useAppSelector } from "./app/hooks";
import AuthContainer from "./features/authentication/AuthContainer";
import { AuthContext } from "./features/authentication/AuthContext";
import AuthenticationProfile from "./features/authentication/AuthenticationProfile";
import ChangeForgottenPassword from "./features/authentication/ChangeForgottenPassword";
import ChangePassword from "./features/authentication/ChangePassword";
import ConfirmationEmailSent from "./features/authentication/ConfirmationEmailSent";
import ConfirmEmail from "./features/authentication/ConfirmEmail";
import DualRoute from "./features/authentication/DualRoute";
import ForgottenPassword from "./features/authentication/ForgottenPassword";
import PrivateRoute from "./features/authentication/ProtectedRoute";
import PublicRoute from "./features/authentication/PublicRoute";
import { companiesState } from "./features/companies/companiesActions";
import CompaniesPage from "./features/companies/companiesPage/CompaniesPage";
import CompaniesSideMenuIndex from "./features/companies/CompaniesSideMenuIndex";
import EditCompany from "./features/companies/EditCompany";
import AssociatesPage from "./features/coworkers/AssociatesPage";
import AddOnboardingDocument from "./features/documents/AddOnboardingDocument";
import Profile from "./features/employees/employeeProfile/Profile";
import EmployeesPage from "./features/employees/EmployeesPage";
import PendingEmployees from "./features/employees/PendingEmployees";
import Attendance from "./features/event/Attendance";
import Events from "./features/events/Events";
import LandingPage from "./features/LandingPage";
import MainWorkTimeRoleSelectionPage from "./features/MainWorkTimeRoleSelecitonPage";
import CreatePayroll from "./features/payroll/creation/Create";
import Index from "./features/payroll/creation/Index";
import ShowPayrolls from "./features/payrolls/PayrollsList";
import { PrivacyPolicy } from "./features/PrivacyPolicy";
import { AuthMode } from "./models/Enums/AuthMode";

const AppRouter = () => {
  const { user } = useContext(AuthContext);
  const { activeCompanies } = useAppSelector(companiesState);

  return (
    <Routes>
      <Route
        path="/"
        element={
          <DualRoute
            nonAuthenticatedRoute={<LandingPage />}
            authenticatedRoute={
              (activeCompanies === undefined || activeCompanies.length === 0) &&
              (user.workTimeRoleName === undefined ||
                user.workTimeRoleName === "") ? (
                <MainWorkTimeRoleSelectionPage />
              ) : (
                <CompaniesPage />
              )
            }
          />
        }
      />
      <Route
        path="/auth/registration"
        element={
          <PublicRoute>
            <AuthContainer initialAuthMode={AuthMode.Registration} />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/login/:email?"
        element={
          <PublicRoute>
            <AuthContainer initialAuthMode={AuthMode.Login} />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/confirmation-email-sent/:email"
        element={
          <PublicRoute>
            <ConfirmationEmailSent />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/change-forgotten-password/:email/:code"
        element={
          <PublicRoute>
            <ChangeForgottenPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/forgotten-password/:email?"
        element={
          <PublicRoute>
            <ForgottenPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/confirm-email"
        element={
          <PublicRoute>
            <ConfirmEmail />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/change-password"
        element={
          <PrivateRoute>
            <ChangePassword />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/payroll/create"
        element={
          <PrivateRoute>
            <CreatePayroll />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/onboardingdocument/add"
        element={
          <PrivateRoute>
            <AddOnboardingDocument />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/pending-employees"
        element={
          <PrivateRoute>
            <PendingEmployees />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/employees"
        element={
          <PrivateRoute>
            <EmployeesPage />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/payrolls"
        element={
          <PrivateRoute>
            <ShowPayrolls />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/events"
        element={
          <PrivateRoute>
            <Events />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/auth/profile"
        element={
          <PrivateRoute>
            <AuthenticationProfile />
          </PrivateRoute>
        }
      />
      <Route path="/:companyId/payroll" element={<Index />} />
      <Route
        path="/:companyId/company/index"
        element={
          <PrivateRoute>
            <CompaniesSideMenuIndex />
          </PrivateRoute>
        }
      />

      {/* <Route
        path="/company-structure"
        element={
          <PrivateRoute>
            <CompanyStructure />
          </PrivateRoute>
        }
      /> */}

      {/* <Route
        path="/:companyId/employees/create"
        element={
          <PrivateRoute>
            <CreateEmployee />
          </PrivateRoute>
        }
      /> */}
      <Route
        path="/:companyId/edit-company"
        element={
          <PrivateRoute>
            <EditCompany />
          </PrivateRoute>
        }
      />
      <Route path="/:companyId/privacy-policy" element={<PrivacyPolicy />} />
      <Route
        path="/:companyId/employees/:id"
        element={
          <PrivateRoute>
            <Profile />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/employees/associates"
        element={
          <PrivateRoute>
            <AssociatesPage />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/attendance"
        element={
          <PrivateRoute>
            <Attendance />
          </PrivateRoute>
        }
      />

      {/* Routes that include companyId */}
      <Route
        path="/:companyId/payroll/create"
        element={
          <PrivateRoute>
            <CreatePayroll />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/employees"
        element={
          <PrivateRoute>
            <EmployeesPage />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/payrolls"
        element={
          <PrivateRoute>
            <ShowPayrolls />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/events"
        element={
          <PrivateRoute>
            <Events />
          </PrivateRoute>
        }
      />
      <Route
        path="/:companyId/edit-company"
        element={
          <PrivateRoute>
            <EditCompany />
          </PrivateRoute>
        }
      />
    </Routes>
  );
};

export default AppRouter;
