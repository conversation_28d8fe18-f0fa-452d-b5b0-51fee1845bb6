import {
  Change<PERSON>vent,
  MouseEvent,
  useContext,
  useEffect,
  useState,
} from "react";
import ReCAPTCHA from "react-google-recaptcha";
import { useNavigate, useParams } from "react-router-dom";
import styled from "styled-components";
import { useAppDispatch } from "../../app/hooks";
import Container from "../../components/Container";
import Form from "../../components/Form/Form";
import Alert from "../../components/Inputs/Alert";
import Button from "../../components/Inputs/Button";
import Checkbox from "../../components/Inputs/Checkbox";
import EmailBox from "../../components/Inputs/EmailBox";
import PasswordBox from "../../components/Inputs/PasswordBox";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../constants/local-storage-constants";
import { LoginUserDTO } from "../../models/DTOs/LoginUserDTO";
import { login } from "../../services/authentication/authenticationService";
import { isValidUsername } from "../../services/emailService";
import { onGetCompanies } from "../companies/companiesActions";
import { AuthContext } from "./AuthContext";
const envVariable = import.meta.env.VITE_GATEWAY_API;

const LoginButton = styled(Button)`
  width: 100%;
`;

const FooterContainer = styled(Container)`
  display: flex;
  margin-top: 0.4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  width: 100%;
  position: relative;
`;

const RememberMeCheckbox = styled(Checkbox)`
  position: absolute;
  left: 1rem;
`;

const ForgottenPasswordButton = styled(Button)`
  position: absolute;
  right: 3.125rem;
  background: none;
  color: black;
  font-size: 1rem;
  font-weight: 400;
  padding: 0;

  &:hover {
    color: var(--button-forgotten-password-color-hover);
  }
`;

const ReCAPTCHAContainer = styled(ReCAPTCHA)`
  margin: 0 auto;
  width: 19rem;
  margin-top: 3rem;

  iframe {
    border-radius: 2.9rem;
    border: var(--iframe-color) 0.063rem solid;
    border-top: 0;
    width: 18.75rem;
    height: 4.625rem;
    padding-right: 0.2rem;
  }
`;

interface Props {
  returnAfterLogin?: string;
}

const Login = ({ returnAfterLogin }: Props) => {
  const { user, setUser } = useContext(AuthContext);
  const [isCaptchaValid, setIsCaptchaValid] = useState(
    envVariable === "Testing"
  );
  const [rememberMeChecked, setRememberMeChecked] = useState(true);
  const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
  const { email } = useParams();
  const [currentUser, setCurrentUser] = useState({
    email: email,
  } as LoginUserDTO);
  const [isLoignBtnActive, setIsLoginBtnActive] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (user && user.email) {
      navigate(returnAfterLogin ?? "/");
    }
  }, [user]);

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCurrentUser({
      ...currentUser,
      email: e.currentTarget.value.trim(),
    });
    setShowErrorMessage(false);

    setIsLoginBtnActive(
      isCaptchaValid &&
        isValidUsername(e.currentTarget.value.trim()) &&
        currentUser.password.length > 0
    );
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCurrentUser({
      ...currentUser,
      password: e.currentTarget.value,
    });
    setShowErrorMessage(false);

    setIsLoginBtnActive(
      isCaptchaValid &&
        isValidUsername(currentUser.email) &&
        e.currentTarget.value.length > 0
    );
  };

  const handleLoginSubmit = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const userDTO = await login(currentUser, rememberMeChecked);
    if (!userDTO) {
      setShowErrorMessage(true);
      return;
    }

    localStorage.setItem(
      LOCAL_STORAGE_WORKTIME_ROLE_NAME,
      userDTO.workTimeRole?.name ?? ""
    );

    setUser({
      userId: userDTO.id,
      email: currentUser.email,
      hasSignedIn: userDTO.hasSignedIn,
      workTimeRoleName: userDTO.workTimeRole?.name ?? "",
    });

    dispatch(onGetCompanies());

    navigate(returnAfterLogin ?? "/");
  };

  const onRememberMeClicked = () => {
    setRememberMeChecked(!rememberMeChecked);
  };

  const handleForgottenPasswordClicked = () => {
    navigate(`/auth/forgotten-password/${currentUser.email ?? ""}`);
  };

  const handleCaptchaChange = (value: string | null) => {
    setIsCaptchaValid(value !== undefined && value !== null);

    setIsLoginBtnActive(
      value !== undefined &&
        value !== null &&
        isValidUsername(currentUser.email) &&
        currentUser.password.length > 0
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && isLoignBtnActive) {
      handleLoginSubmit(e as unknown as MouseEvent<HTMLButtonElement>);
    }
  };

  return (
    <Form>
      <EmailBox
        name="email"
        handleChange={handleEmailChange}
        label="E-mail/Username"
        placeholder="Fill your email or username"
        alertMessage="Invalid email or username"
        value={currentUser.email}
        onKeyDown={handleKeyDown}
        validateUsername={true}
        data-testid="email-box"
      />
      <PasswordBox
        name="password"
        handleChange={handlePasswordChange}
        label="Password"
        placeholder="Fill your password"
        type="password"
        value={currentUser.password}
        onKeyDown={handleKeyDown}
        data-testid="password-box"
      />
      {showErrorMessage && (
        <Alert
          message="strWrongCredentials"
          type="error"
          data-testid="error-alert"
        />
      )}
      <LoginButton
        onClick={handleLoginSubmit}
        label="Login"
        disabled={!isLoignBtnActive}
        data-testid="login-button"
      />
      <FooterContainer data-testid="footer-container">
        <RememberMeCheckbox
          isChecked={rememberMeChecked}
          handleChange={onRememberMeClicked}
          label="Remember me"
          name="rememberMe"
          data-testid="remember-me-checkbox"
        />
        <ForgottenPasswordButton
          label="Forgotten password?"
          onClick={handleForgottenPasswordClicked}
          data-testid="forgotten-password-button"
        />
      </FooterContainer>
      {envVariable !== "Testing" && (
        <ReCAPTCHAContainer
          sitekey="6Lc75cIoAAAAAIkkiXv_yl4pWr3IwrMmi93u_lRB"
          onChange={handleCaptchaChange}
          data-testid="recaptcha-container"
        />
      )}
    </Form>
  );
};

export default Login;
