import { useEffect, useState } from "react";
import styled from "styled-components";
import Container from "../../../components/Container";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1rem;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  background-color: #45b6f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: #333;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: any;
}

interface PayrollData {
  baseSalary: string;
  position: string;
  department: string;
  startDate: string;
  workingHours: string;
  overtimeRate: string;
  bonuses: string;
  deductions: string;
}

const NewPayroll = ({ onValidation, data = {} }: Props) => {
  const [formData, setFormData] = useState<PayrollData>({
    baseSalary: data.baseSalary || "",
    position: data.position || "",
    department: data.department || "",
    startDate: data.startDate || "",
    workingHours: data.workingHours || "",
    overtimeRate: data.overtimeRate || "",
    bonuses: data.bonuses || "",
    deductions: data.deductions || "",
  });

  useEffect(() => {
    const isValid =
      formData.baseSalary.trim() !== "" &&
      formData.position.trim() !== "" &&
      formData.department.trim() !== "" &&
      formData.startDate.trim() !== "";

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: PayrollData) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <FormContainer>
      {/* Employment Information Section */}
      <SectionHeader>
        <SectionIcon>💼</SectionIcon>
        <SectionTitle>Трудова информация</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="position"
            label="Длъжност"
            value={formData.position}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="department"
            label="Отдел"
            value={formData.department}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="startDate"
            label="Дата на започване"
            value={formData.startDate}
            handleChange={handleChange}
            placeholder="01.01.2024"
          />
        </FormField>
        <FormField>
          <Textbox
            name="workingHours"
            label="Работни часове"
            value={formData.workingHours}
            handleChange={handleChange}
            placeholder="8"
          />
        </FormField>
      </FormRow>

      {/* Salary Information Section */}
      <SectionHeader>
        <SectionIcon>💰</SectionIcon>
        <SectionTitle>Заплащане</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="baseSalary"
            label="Основна заплата"
            value={formData.baseSalary}
            handleChange={handleChange}
            placeholder="1500.00"
          />
        </FormField>
        <FormField>
          <Textbox
            name="overtimeRate"
            label="Ставка извънреден труд"
            value={formData.overtimeRate}
            handleChange={handleChange}
            placeholder="1.5"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="bonuses"
            label="Бонуси"
            value={formData.bonuses}
            handleChange={handleChange}
            placeholder="0.00"
          />
        </FormField>
        <FormField>
          <Textbox
            name="deductions"
            label="Удръжки"
            value={formData.deductions}
            handleChange={handleChange}
            placeholder="0.00"
          />
        </FormField>
      </FormRow>
    </FormContainer>
  );
};

export default NewPayroll;
