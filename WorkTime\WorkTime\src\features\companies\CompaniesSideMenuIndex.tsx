import { useEffect, useState } from "react";
import styled, { css } from "styled-components";
import moveRightImage from "../../assets/images/button/moveRight.png";
import moveRightDisabledImage from "../../assets/images/button/moveRightDisabled.png";
import moveRightHoverImage from "../../assets/images/button/moveRightHover.png";
import plusImage from "../../assets/images/button/plus.png";
import plusDisabledImage from "../../assets/images/button/plusDisable.png";
import plusHoverImage from "../../assets/images/button/plusHover.png";
import importImage from "../../assets/images/button/Sendera.png";
import importImageDisabled from "../../assets/images/button/SenderaDisabled.png";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import CompaniesListSideMenu from "./companiesPage/CompaniesListSideMenu";
import CreateCompany from "./CreateCompany";
import ImportCompany from "./ImportCompany";
import JoinCompany from "./JoinCompany";

const ButtonsContainer = styled(Container)`
  margin-bottom: 0.625rem;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 0.5rem;
  grid-row-gap: 0;
`;

const ContentContainer = styled(Container)`
  position: absolute;
  top: 15rem;
  overflow: auto;

  ::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
  bottom: 3rem;
  left: 1rem;
  right: 1rem;
`;

const CompanyButton = styled(Button)<{
  companyImage: string;
  companyHoverImage: string;
  companyDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--company-button-background-color);
  color: var(--company-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.companyImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 1rem 0 1rem 3.5rem;
  text-align: left;
  cursor: default;

  ${(p) => p.isDisable && Disable}

  ${(p) =>
    p.isDisable &&
    css`
      &:hover {
        background-image: url(${p.companyHoverImage});
        background-color: var(--company-button-background-color-hover);
        color: var(--company-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const Disable = css<{
  companyDisabledImage: string;
}>`
  background-image: url(${(p) => p.companyDisabledImage});
  background-color: var(--company-button-background-color-disable);
  color: var(--company-button-color-disable);
`;

enum TabItems {
  Import = "import",
  Create = "create",
  Join = "join",
  MyCompanies = "myCompanies",
}

const CompaniesSideMenuIndex = () => {
  const [company, setCompany] = useState({} as CompanyDTO);
  const [activeTab, setActiveTab] = useState("myCompanies");

  useEffect(() => {
    if (Object.keys(company || {}).length === 0) {
      setCompany({
        name: "Фирми за импорт",
        bulstat: "",
        contactName: "",
        userRegistrationCompanyId: 0,
        id: "",
      });
    }
  }, [company]);

  const handleSetTab = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <>
      <ButtonsContainer>
        <CompanyButton
          label="My Companies"
          companyImage={moveRightImage}
          companyHoverImage={moveRightHoverImage}
          companyDisabledImage={moveRightDisabledImage}
          onClick={() => handleSetTab("myCompanies")}
          isDisable={activeTab !== "myCompanies"}
          data-testid="my-companies-button"
        />
        <CompanyButton
          label="strAddCompanySideMenu"
          companyImage={plusImage}
          companyHoverImage={plusHoverImage}
          companyDisabledImage={plusDisabledImage}
          onClick={() => handleSetTab("create")}
          isDisable={activeTab !== "create"}
          data-testid="add-company-button"
        />
        <CompanyButton
          label="Import"
          companyImage={importImage}
          companyHoverImage={importImageDisabled}
          companyDisabledImage={importImageDisabled}
          onClick={() => handleSetTab("import")}
          isDisable={activeTab !== "import"}
          data-testid="import-button"
        />
        <CompanyButton
          label="Join company"
          companyImage={moveRightImage}
          companyHoverImage={moveRightHoverImage}
          companyDisabledImage={moveRightDisabledImage}
          onClick={() => handleSetTab("join")}
          isDisable={activeTab !== "join"}
          data-testid="join-company-button"
        />
      </ButtonsContainer>
      <ContentContainer>
        {activeTab === TabItems.Import && <ImportCompany />}
        {activeTab === TabItems.MyCompanies && <CompaniesListSideMenu />}
        {activeTab === TabItems.Create && <CreateCompany />}
        {activeTab === TabItems.Join && <JoinCompany />}
      </ContentContainer>
    </>
  );
};

export default CompaniesSideMenuIndex;
