import { useEffect, useState } from "react";
import styled from "styled-components";
import Container from "../../../components/Container";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1rem;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  background-color: #45b6f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: #333;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const SingleFieldRow = styled(Container)`
  display: flex;
  flex-direction: row;
  margin-bottom: 1rem;
  width: 100%;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: any;
}

interface FormData {
  city: string;
  postalCode: string;
  region: string;
  municipality: string;
  quarter: string;
  street: string;
  block: string;
  apartment: string;
  phone: string;
  workPhone: string;
  email: string;
  workEmail: string;
}

const NewAddress = ({ onValidation, data = {} }: Props) => {
  const [formData, setFormData] = useState<FormData>({
    city: data.city || "",
    postalCode: data.postalCode || "",
    region: data.region || "",
    municipality: data.municipality || "",
    quarter: data.quarter || "",
    street: data.street || "",
    block: data.block || "",
    apartment: data.apartment || "",
    phone: data.phone || "",
    workPhone: data.workPhone || "",
    email: data.email || "",
    workEmail: data.workEmail || "",
  });

  useEffect(() => {
    const isValid =
      formData.city.trim() !== "" &&
      formData.postalCode.trim() !== "" &&
      formData.region.trim() !== "" &&
      formData.municipality.trim() !== "";

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: FormData) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <FormContainer>
      {/* Address Section */}
      <SectionHeader>
        <SectionIcon>📍</SectionIcon>
        <SectionTitle>Адрес</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="city"
            label="Град"
            value={formData.city}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="postalCode"
            label="ПК"
            value={formData.postalCode}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="region"
            label="Област"
            value={formData.region}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="municipality"
            label="Община"
            value={formData.municipality}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="quarter"
            label="Квартал"
            value={formData.quarter}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="street"
            label="Улица"
            value={formData.street}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="block"
            label="Блок"
            value={formData.block}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="apartment"
            label="Апартамент"
            value={formData.apartment}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      {/* Phone Section */}
      <SectionHeader>
        <SectionIcon>📞</SectionIcon>
        <SectionTitle>Телефон</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="phone"
            label="Телефон"
            value={formData.phone}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="workPhone"
            label="Служ. телефон"
            value={formData.workPhone}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      {/* E-mail Section */}
      <SectionHeader>
        <SectionIcon>📧</SectionIcon>
        <SectionTitle>E-mail</SectionTitle>
      </SectionHeader>

      <SingleFieldRow>
        <FormField>
          <Textbox
            name="email"
            label="E-mail"
            value={formData.email}
            handleChange={handleChange}
          />
        </FormField>
      </SingleFieldRow>

      <SingleFieldRow>
        <FormField>
          <Textbox
            name="workEmail"
            label="Служебен E-mail"
            value={formData.workEmail}
            handleChange={handleChange}
          />
        </FormField>
      </SingleFieldRow>
    </FormContainer>
  );
};

export default NewAddress;
