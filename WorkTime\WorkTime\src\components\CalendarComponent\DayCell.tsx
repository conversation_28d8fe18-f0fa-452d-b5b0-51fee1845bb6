import React from "react";
import styled from "styled-components";
import DayBox from "./DayBox";
import { EmployeeInfo } from "./types/types";
import Container from "../Container";

interface DayCellProps {
  dayNumber: number;
  isCurrentMonth: boolean;
  isCurrentDay: boolean;
  missingEmployees: EmployeeInfo[];
  numberOfRows: number;
  selectedEmployeeId?: string;
  hoveredEmployeeId?: string;
}

const ContainerDay = styled(Container)<{
  isCurrentMonth: boolean;
  numberOfRows: number;
}>`
  display: grid;
  position: relative;

  grid-template-columns: repeat(1, 1fr);
  padding: 0.2em 0em 0.4em 0.2em;

  height: ${({ numberOfRows }) =>
    numberOfRows < 3
      ? `calc(2.5em + ${2 * 2}em)`
      : `calc(2.5em + ${numberOfRows * 2}em)`};

  font-size: clamp(0.75em, 2vw, 1em);
  cursor: pointer;

  background-color: ${({ isCurrentMonth }) =>
    isCurrentMonth
      ? "var(--datepicker-view-background-color)"
      : "var(--datepicker-view-buttons-color)"};

  color: ${({ isCurrentMonth }) =>
    isCurrentMonth
      ? "var(--absence-button-color-disable)"
      : "var(--datepicker-view-buttons-font-color-not-current-month)"};

  &::after,
  &::before {
    content: "";
    position: absolute;
    background-color: ${({ isCurrentMonth }) =>
      isCurrentMonth
        ? "var(--datepicker-view-buttons-color)"
        : "var(--datepicker-view-background-color)"};
    z-index: 999;
  }

  &::after {
    top: -0.1em;
    left: 0;
    width: 100vw;
    height: 0.075em;
  }

  &::before {
    top: 0;
    left: -0.12em;
    width: 0.05em;
    height: 100vh;
  }

  user-select: none;
`;

const Circle = styled(Container)<{ isCurrentDay: boolean }>`
  width: 1.2em;
  height: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25em;
  border-radius: 50%;
  border: ${({ isCurrentDay }) =>
    isCurrentDay
      ? "0.125rem solid var(--datepicker-selected-day-color)"
      : "0.125rem solid transparent"};
`;

export const DayCell: React.FC<DayCellProps> = ({
  dayNumber,
  isCurrentMonth,
  isCurrentDay,
  missingEmployees,
  numberOfRows,
  selectedEmployeeId,
  hoveredEmployeeId,
}) => {
  return (
    <ContainerDay
      data-testid="day-cell-container"
      isCurrentMonth={isCurrentMonth}
      numberOfRows={numberOfRows}
    >
      <Circle
        data-testid="day-cell-circle"
        isCurrentDay={isCurrentDay && isCurrentMonth}
      >
        {dayNumber}
      </Circle>
      <DayBox
        data-testid="day-cell-box"
        date={dayNumber}
        dayData={missingEmployees}
        numberOfRows={numberOfRows}
        selectedEmployeeId={selectedEmployeeId}
        hoveredEmployeeId={hoveredEmployeeId}
      />
    </ContainerDay>
  );
};
