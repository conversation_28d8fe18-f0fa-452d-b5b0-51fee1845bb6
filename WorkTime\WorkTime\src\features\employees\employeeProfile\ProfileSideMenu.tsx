import styled from "styled-components";
import MainWindowContainer from "../../../components/MainWindowContainer";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import { css } from "styled-components";
import keyImage from "../../../assets/images/button/key.png";
import keyImageDisable from "../../../assets/images/button/keyInactive.png";
import userImage from "../../../assets/images/button/user.png";
import userImageDisable from "../../../assets/images/button/userInactive.png";
import userGroupsImage from "../../../assets/images/button/userGroups.png";
import userGroupsImageDisable from "../../../assets/images/button/userGroupsInactive.png";
import buildingsImage from "../../../assets/images/button/buildings.png";
import buildingsImageDisable from "../../../assets/images/button/buildingsInactive.png";
import { useEffect, useState } from "react";
import MyProfileSideMenu from "./MyProfileSideMenu";
import ChangePassword from "../../authentication/ChangePassword";
import CompaniesSideMenuIndex from "../../companies/CompaniesSideMenuIndex";
import { useAppSelector } from "../../../app/hooks";
import { companiesState } from "../../companies/companiesActions";
import CoworkersSideMenu from "../../coworkers/CoworkersSideMenu";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const ButtonsContainer = styled(Container)<{
  hide: boolean;
}>`
  display: ${({ hide }) => (hide ? "none" : "grid")};
  margin-bottom: 0.625rem;
  width: 100%;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 0.5rem;
  grid-row-gap: 0;
`;

const ProfileButton = styled(Button)<{
  profileImage: string;
  profileHoverImage: string;
  profileDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--profile-button-background-color);
  color: var(--profile-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.profileImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 1rem 0 1rem 3.1rem;
  text-align: left;
  cursor: default;

  ${(p) => p.isDisable && Disable}

  ${(p) =>
    p.isDisable &&
    css`
      &:hover {
        background-image: url(${p.profileHoverImage});
        background-color: var(--profile-button-background-color-hover);
        color: var(--profile-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const Disable = css<{
  profileDisabledImage: string;
}>`
  background-image: url(${(p) => p.profileDisabledImage});
  background-color: var(--profile-button-background-color-disable);
  color: var(--profile-button-color-disable);
`;

interface ProfileSideMenuProps {
  onButtonClick: (button: string) => void;
  goBack: boolean;
  initialPage?: string;
}

const ProfileSideMenu: React.FC<ProfileSideMenuProps> = ({
  onButtonClick,
  goBack,
  initialPage = "profile",
}) => {
  const [activeButton, setActiveButton] = useState(initialPage);

  useEffect(() => {
    if (goBack && activeButton === "myCompanies") {
      handleButtonClick("profile");
    }
  }, [goBack, activeButton]);

  useEffect(() => {
    setActiveButton(initialPage);
  }, [initialPage]);

  const handleButtonClick = (button: string) => {
    setActiveButton(button);
    onButtonClick(button);
  };

  return (
    <MainContainer data-testid="profile-side-menu">
      <ButtonsContainer
        hide={activeButton === "myCompanies"}
        data-testid="buttons-container"
      >
        <ProfileButton
          label="Profile"
          profileImage={userImage}
          profileDisabledImage={userImageDisable}
          profileHoverImage={userImageDisable}
          isDisable={activeButton !== "profile"}
          onClick={() => handleButtonClick("profile")}
          data-testid="profile-button"
        />
        <ProfileButton
          label="Password"
          profileImage={keyImage}
          profileDisabledImage={keyImageDisable}
          profileHoverImage={keyImageDisable}
          isDisable={activeButton !== "password"}
          onClick={() => handleButtonClick("password")}
          data-testid="password-button"
        />
        <ProfileButton
          label="My Companies"
          profileImage={buildingsImage}
          profileDisabledImage={buildingsImageDisable}
          profileHoverImage={buildingsImageDisable}
          isDisable={activeButton !== "myCompanies"}
          onClick={() => handleButtonClick("myCompanies")}
          data-testid="my-companies-button"
        />
        <ProfileButton
          label="Coworkers"
          profileImage={userGroupsImage}
          profileDisabledImage={userGroupsImageDisable}
          profileHoverImage={userGroupsImageDisable}
          isDisable={activeButton !== "coworkers"}
          onClick={() => handleButtonClick("coworkers")}
          data-testid="coworkers-button"
        />
      </ButtonsContainer>
      <div data-testid="content-container">
        {activeButton === "profile" && (
          <MyProfileSideMenu data-testid="my-profile-menu" />
        )}
        {activeButton === "password" && (
          <ChangePassword data-testid="change-password" />
        )}
        {activeButton === "coworkers" && (
          <CoworkersSideMenu data-testid="coworkers-menu" />
        )}
        {activeButton === "myCompanies" && (
          <CompaniesSideMenuIndex data-testid="companies-menu" />
        )}
      </div>
    </MainContainer>
  );
};

export default ProfileSideMenu;
