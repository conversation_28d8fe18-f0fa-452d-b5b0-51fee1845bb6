import styled from "styled-components";
import Translator from "../services/language/Translator";
import UserNavMenu from "./authentication/UserNavMenu";
import LanguageSelector from "./LanguageSelector";
import MainWindow from "./MainWindow";
import { Link, useLocation } from "react-router-dom";
import { useContext, useEffect, useRef, useState } from "react";
import { useMenu } from "./MenuContext";
import { AuthContext } from "./authentication/AuthContext";

const NavContainer = styled.div`
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.7rem 0;
  background-color: var(--app-header-color);
`;

const NavigationLink = styled(Link)`
  text-decoration: none;
  margin-left: 1.5rem;
`;

const AppLabel = styled.label`
  letter-spacing: var(--unnamed-character-spacing-0);
  color: var(--app-label-color);
  font-size: 2rem;
  font-weight: Montserrat;
  letter-spacing: 0;

  &:hover {
    cursor: pointer;
  }
`;

const Layout = () => {
  const menuRef = useRef<HTMLDivElement | null>(null);
  const location = useLocation();
  const { isOpen, toggleMenu, closeMenu } = useMenu();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    setIsAuthenticated(user.email ? true : false);
  }, [user, isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !isOpen
      ) {
        closeMenu();
      }
    };
    window.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      {isAuthenticated || location.pathname !== "/" ? (
        <NavContainer data-testid="nav-container">
          <NavigationLink to="/" data-testid="nav-link">
            <AppLabel data-testid="app-label">
              <Translator getString="WorkTime" />
            </AppLabel>
          </NavigationLink>
          <UserNavMenu onClick={toggleMenu} data-testid="user-nav-menu" />
          <LanguageSelector data-testid="language-selector" />
        </NavContainer>
      ) : (
        <></>
      )}

      <MainWindow
        isMenuOpen={isOpen}
        menuRef={menuRef}
        onClick={toggleMenu}
        data-testid="main-window"
      />
    </>
  );
};

export default Layout;
