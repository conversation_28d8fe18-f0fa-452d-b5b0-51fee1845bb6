import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from "react";
import styled from "styled-components";
import Container from "../Container";
import Image from "../Image";
import checkIcon from "../../assets/images/combobox/checkmark.png";
import TriangularArrow from "../../assets/images/arrows/down-filled-triangular-arrow.png";
import Translator from "../../services/language/Translator";

const ComboboxContainer = styled.div<{ width?: string }>`
  position: relative;
  display: block;
  align-items: center;
  margin: 0.45rem 0rem;
  min-width: 10rem;
`;

const SelectedDisplay = styled(Container)<StyledProps>`
  box-sizing: border-box;
  border-radius: 1.625rem;
  border: 0.063rem;
  padding: 0.8rem;
  background-color: var(--combobox-display-background-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-radius 0.5s ease;
  position: relative;
  z-index: 10;
  color: var(--combobox-header-text-color);
  font-family: Segoe UI;
  min-width: 8rem;
  font-weight: 400;
  color: var(--combobox-header-text-color);
  overflow: hidden;

  box-shadow: -0.05rem -0.05rem 0.05rem var(--comboboxShadow),
    0.05rem -0.05rem 0.05rem var(--comboboxShadow),
    0rem -0.05rem 0.05rem var(--comboboxShadow);

  ${({ isOpen }) =>
    isOpen &&
    `
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  `}
`;
const TriangularArrowContainer = styled(Image)`
  width: 0.65rem;
  height: 0.65rem;
  opacity: 0.5;

  ${SelectedDisplay}:hover & {
    opacity: 1;
  }
`;
const DropdownList = styled(Container)<StyledProps>`
  box-sizing: border-box;
  position: absolute;
  grid-column: 2/2;
  top: 75%;
  width: 100%;
  padding-top: 0.6rem;
  padding-bottom: 1rem;
  background-color: var(--combobox-dropdown-background-color);
  overflow-y: auto;
  z-index: 9;
  opacity: ${({ isOpen }) => (isOpen ? "1" : "0")};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  transition: all 0.3s ease-in-out;
  max-height: 30rem;

  border-bottom-left-radius: 2rem;
  border-bottom-right-radius: 2rem;

  box-shadow: -0.05rem 0.05rem 0.05rem var(--comboboxShadow),
    0.05rem 0.05rem 0.05rem var(--comboboxShadow),
    0px 0.05rem 0.05rem var(--comboboxShadow);
`;

const OptionItem = styled(Container)`
  padding: 0.3rem 1rem;
  display: flex;
  font-size: 1rem;
  align-items: flex-start;
  justify-content: flex-start;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  z-index: 9;
  font-family: segoe-ui;
  font-weight: normal;
  color: var(--combobox-header-text-color);
  gap: 0.5rem;

  &:last-child {
    border-bottom-left-radius: 3.5rem;
    border-bottom-right-radius: 3.5rem;
  }
`;

const CheckboxContainer = styled(Container)`
  display: flex;
  align-items: flex-start;
  padding-top: 0.2rem;
  flex-shrink: 0;
`;

const OptionText = styled.span`
  flex: 1;
  text-align: left;
  word-break: break-word;
`;

const CheckmarkImage = styled(Image)<{ isHovered?: boolean }>`
  position: absolute;
  left: 35%;
  top: 30%;
  transform: translate(-25%, -50%) scale(1.2);
  width: 1.1rem;
  height: 1rem;
  opacity: ${({ isHovered }) => (isHovered ? "0.5" : "1")};
  transition: all 0.2s ease;
  pointer-events: none;
`;

const RoundCheckbox = styled(Container)<{ checked: boolean }>`
  position: relative;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  border-radius: 50%;
  border: 0.12rem solid var(--combobox-checkbox-option-border-color);
  background-color: var(--combobox-checkbox-background-color);
  transition: all 0.2s ease;
  overflow: visible;

  ${CheckmarkImage} {
    opacity: 0;
    visibility: hidden;
  }

  ${({ checked }) =>
    checked &&
    `
    ${CheckmarkImage} {
      opacity: 1;
      visibility: visible;
    }
  `}

  &:hover ${CheckmarkImage} {
    opacity: ${({ checked }) => (checked ? "1" : "0.5")};
    visibility: visible;
  }
`;

const SelectedCount = styled.span`
  color: var(--combobox-header-text-color);
  font-size: 0.9em;
  margin-right: 1rem;
`;

interface Option {
  id: string | number;
  label: string;
  value: any;
}

interface ComboboxMultipleChoicesProps {
  options: Option[];
  selectedValues: any[];
  onChange: (selected: any[]) => void;
  placeholder?: string;
  className?: string;
  width?: string;
  height?: string;
}

interface StyledProps {
  isOpen: boolean;
  height?: string;
  width?: string;
}

const ComboboxMultipleChoices: React.FC<ComboboxMultipleChoicesProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder,
  width,
  height,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredOption, setHoveredOption] = useState<string | null>(null);

  const containerRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);
  const handleOptionClick = useCallback(
    (value: any) => {
      const newSelected = selectedValues.includes(value)
        ? selectedValues.filter((v) => v !== value)
        : [...selectedValues, value];
      onChange(newSelected);
    },
    [selectedValues, onChange]
  );

  const displayText = useMemo(() => {
    if (selectedValues.length === 0) return placeholder || "";
    if (selectedValues.length === 1) {
      const option = options.find(
        (option) => option.value === selectedValues[0]
      );
      return option?.label || "";
    }
    return (
      <SelectedCount>
        {`${selectedValues.length} `}
        <Translator getString="strItemsSelected" />
      </SelectedCount>
    );
  }, [selectedValues, options, placeholder]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <ComboboxContainer
      ref={containerRef}
      width={width}
      data-testid="combobox-container"
    >
      <SelectedDisplay
        onClick={toggleDropdown}
        isOpen={isOpen && options.length > 0}
        width={width}
        height={height}
        data-testid="selected-display"
      >
        {typeof displayText === "string" ? (
          <Translator
            getString={displayText}
            data-testid="display-text-translator"
          />
        ) : (
          displayText
        )}
        <TriangularArrowContainer
          src={TriangularArrow}
          data-testid="dropdown-arrow"
        />
      </SelectedDisplay>

      <DropdownList
        isOpen={isOpen && options.length > 0}
        width={width}
        data-testid="dropdown-list"
      >
        {options.map((option) => (
          <OptionItem
            key={option.id}
            onClick={() => handleOptionClick(option.value)}
            onMouseEnter={() => setHoveredOption(option.value)}
            onMouseLeave={() => setHoveredOption(null)}
            data-testid={`option-item-${option.id}`}
          >
            <CheckboxContainer data-testid={`checkbox-container-${option.id}`}>
              <RoundCheckbox
                checked={selectedValues.includes(option.value)}
                data-testid={`checkbox-${option.id}`}
              >
                <CheckmarkImage
                  src={checkIcon}
                  isHovered={
                    !selectedValues.includes(option.value) &&
                    hoveredOption === option.value
                  }
                  data-testid={`checkmark-${option.id}`}
                />
              </RoundCheckbox>
            </CheckboxContainer>
            <OptionText>{option.label}</OptionText>
          </OptionItem>
        ))}
      </DropdownList>
    </ComboboxContainer>
  );
};

export default ComboboxMultipleChoices;
