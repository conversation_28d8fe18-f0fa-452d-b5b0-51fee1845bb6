import { useContext, useEffect, useState } from "react";
import styled from "styled-components";
import { Header } from "../components/Header";
import MainWindowContainer from "../components/MainWindowContainer";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../constants/local-storage-constants";
import { RoleDTO } from "../models/DTOs/RoleDTO";
import { HeaderType } from "../models/Enums/HeaderType";
import {
  getRoles,
  setWorkTimeRole,
} from "../services/authorization/authorizationService";
import Translator from "../services/language/Translator";
import { AuthContext } from "./authentication/AuthContext";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 3rem;
  margin: 1rem;
  min-height: 500px;
  height: 100%;
  overflow-y: auto;
  position: relative;
`;

const StyledHeader = styled(Header)`
  margin-top: 0;
  margin-bottom: 0rem;
  width: 100%;
  text-align: left;
  font-weight: 400;
  font-size: 1.3rem;
`;

const ToggleButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 23rem;
  height: 11rem;
  background-color: #ffffff33;
  border-radius: 20px;
  border: 2px solid white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0 1.5rem;
  cursor: pointer;
`;

const EmployeeLabel = styled.div<{ isEmployee: boolean }>`
  font-size: 1rem;
  font-weight: 400;
  user-select: none;
  color: ${(props) => (props.isEmployee ? "#2D2D2D" : "#BCC2CF")};
`;

const RolesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
`;

const RoleItem = styled.div<{ isEmployee: boolean }>`
  font-size: 1rem;
  user-select: none;
  color: ${(props) => (!props.isEmployee ? "#2D2D2D" : "#BCC2CF")};
`;

const ToggleSwitch = styled.div`
  width: 40px;
  height: 21px;
  background-color: white;
  border-radius: 14px;
  position: relative;
  margin: 0 0.5rem;
`;

const ToggleButton = styled.div<{ isEmployee: boolean }>`
  width: 19px;
  height: 19px;
  background-color: #3498db;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: ${(props) => (props.isEmployee ? "1px" : "20px")};
  transition: left 0.3s ease;
`;

const NextButton = styled.button`
  width: 23rem;
  height: 2.6rem;
  border-radius: 10rem;
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: var(--landing-page-content-color);
  border: none;
  color: var(--landing-page-button-text-color);
  font-size: 1.1rem;
  font-family: "Segoe UI";
  text-decoration: none;

  &:hover {
    background: var(--landing-page-content-hover-color);
    cursor: pointer;
  }
`;

const MainWorkTimeRoleSelectionPage = () => {
  const [isEmployee, setIsEmployee] = useState(true);
  const [roles, setRoles] = useState([] as RoleDTO[]);
  const { user, setUser } = useContext(AuthContext);

  const employeeRole = roles.find((role) => role.name === "grEmployee");
  const ownerRole = roles.find((role) => role.name === "grOwner");

  useEffect(() => {
    getRoles().then((response: RoleDTO[]) => {
      setRoles(response);
    });
  }, []);

  const toggleRole = () => {
    setIsEmployee(!isEmployee);
  };

  const handleNextButtonClick = async () => {
    const roleId = isEmployee ? employeeRole?.id : ownerRole?.id;
    setWorkTimeRole(roleId ?? "").then(() => {
      const roleName = isEmployee ? employeeRole?.name : ownerRole?.name;
      localStorage.setItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME, roleName ?? "");

      setUser({ ...user, workTimeRoleName: roleName });
    });
  };

  return (
    <MainContainer data-testid="main-container">
      <StyledHeader
        data-testid="welcome-header"
        content="Здравейте!"
        headerType={HeaderType.H2}
      />

      <StyledHeader
        data-testid="instruction-header"
        content="Преди да продължим напред, моля да посочите с каква цел ще използвате нашата платформа!"
        headerType={HeaderType.H2}
      />

      <ToggleButtonContainer
        data-testid="toggle-button-container"
        onClick={toggleRole}
      >
        <div
          data-testid="roles-container"
          style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}
        >
          <EmployeeLabel data-testid="employee-label" isEmployee={isEmployee}>
            <Translator getString="grEmployee" />
          </EmployeeLabel>

          <ToggleSwitch data-testid="toggle-switch">
            <ToggleButton data-testid="toggle-button" isEmployee={isEmployee} />
          </ToggleSwitch>

          <RolesList data-testid="roles-list">
            <RoleItem data-testid="owner-role" isEmployee={isEmployee}>
              <Translator getString="grOwner" />
            </RoleItem>
            <RoleItem
              data-testid="company-manager-role"
              isEmployee={isEmployee}
            >
              <Translator getString="grCompanyManager" />
            </RoleItem>
            <RoleItem data-testid="accountant-role" isEmployee={isEmployee}>
              <Translator getString="grAccountant" />
            </RoleItem>
            <RoleItem
              data-testid="human-resources-role"
              isEmployee={isEmployee}
            >
              <Translator getString="grHumanResources" />
            </RoleItem>
          </RolesList>
        </div>
      </ToggleButtonContainer>

      <NextButton data-testid="next-button" onClick={handleNextButtonClick}>
        <Translator getString="strNext" />
      </NextButton>
    </MainContainer>
  );
};

export default MainWorkTimeRoleSelectionPage;
