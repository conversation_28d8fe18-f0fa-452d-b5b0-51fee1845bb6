import React, { useState, useEffect } from "react";
import { styled } from "styled-components";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import Label from "../../components/Inputs/Label";
import Image from "../../components/Image";
import airplane from "../../assets/images/attendancies/airplane.png";
import heart from "../../assets/images/attendancies/heart.png";
import { useMenu } from "../MenuContext";
import { Employee } from "./useFilteredEmployees";
import { AbsencesPageEmployeesList } from "./AbsencesPageEmploeesList";

interface LabelProps {
  boldness?: number;
  fontSize?: number;
  color?: string;
  justifyContent?: string;
  align?: string;
  onClick?: () => void;
}

interface InsideContainerProps {
  variant?: "AbsencesContainer" | "TeamContainer";
}

const OuterContainer = styled(Container)`
  display: block;
`;

const TopContainer = styled(Container)`
  display: flex;
  justify-content: space-between;
  margin-bottom: 1em;
`;

const Icon = styled(Image)<{ marginRight?: number }>`
  margin-right: ${(props) => props.marginRight || 0}rem;
  margin-top: 0.5em;
  margin-bottom: 1.5em;
`;

const CardContainer = styled(Container)<InsideContainerProps>`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  justify-content: start;
  margin-bottom: 1em;
  height: 10rem;
`;

const MainCardContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 16rem;
  width: 12rem;
  border-radius: 1.8rem;
  background-color: var(--attendancies-right-view-card-container-background);
`;

const ButtonContainer = styled(Container)`
  display: flex;
  justify-content: center;
  margin-bottom: 1em;
`;

const StyledButton = styled(Button)`
  width: 15em;
  height: 2.8em;
  transition: 0.3s ease-in-out;

  &:hover {
    background-color: var(--attendancies-right-view-button-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const StyledLabel = styled(Label)<LabelProps>`
  text-align: ${(props) => props.align || "center"};
  color: ${(props) => props.color || "inherit"};
  font-weight: ${(props) => props.boldness || "normal"};
  font-size: ${(props) => (props.fontSize ? `${props.fontSize}px` : "inherit")};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

interface AttendancesRightViewProps {
  selectedEmployee: Employee | null;
  onSelectEmployee: (employee: Employee | null) => void;
  hoveredEmployee?: Employee | null;
  onEmployeeHover: (employee: Employee | null) => void;
}

const AttendancesRightView: React.FC<AttendancesRightViewProps> = ({
  selectedEmployee,
  onSelectEmployee,
  hoveredEmployee,
  onEmployeeHover,
}) => {
  const { toggleMenu } = useMenu();
  const remainingDays = "2";
  const selectedYear = "2025";

  const handleAddAbsence = () => {
    toggleMenu();
  };

  return (
    <>
      <OuterContainer data-testid="outer-container">
        <TopContainer data-testid="top-container">
          <MainCardContainer data-testid="main-card-container-1">
            <Icon
              src={airplane}
              size="medium"
              alt="airplane"
              data-testid="icon-airplane"
            />
            <CardContainer
              variant="AbsencesContainer"
              data-testid="card-container-1"
            >
              <StyledLabel
                children="12"
                boldness={700}
                fontSize={22}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-remaining-days"
              />
              <StyledLabel
                children={Number(remainingDays) == 1 ? "day" : "days"}
                boldness={300}
                fontSize={14}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-days"
              />
              <StyledLabel
                children="remaining"
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-remaining"
              />
              <StyledLabel
                children="Paid Leave"
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-paid-leave"
              />
            </CardContainer>
          </MainCardContainer>
          <MainCardContainer data-testid="main-card-container-2">
            <Icon
              src={heart}
              size="medium"
              alt="heart"
              data-testid="icon-heart"
            ></Icon>
            <CardContainer
              variant="AbsencesContainer"
              data-testid="card-container-2"
            >
              <StyledLabel
                children={remainingDays}
                boldness={700}
                fontSize={22}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-remaining-days-2"
              />
              <StyledLabel
                children={Number(remainingDays) == 1 ? "day" : "days"}
                boldness={300}
                fontSize={14}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-days-2"
              />
              <StyledLabel
                children="used"
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-used"
              />
              <StyledLabel
                children="Sick Leave"
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-sick-leave"
              />
              <StyledLabel
                children={"in"}
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-in"
              />
              <StyledLabel
                children={`${selectedYear}`}
                boldness={400}
                fontSize={16}
                color="var(--attendancies-right-view-card-container-font)"
                data-testid="label-year"
              />
            </CardContainer>
          </MainCardContainer>
        </TopContainer>
        <ButtonContainer data-testid="button-container">
          <StyledButton
            label="Confirm"
            onClick={handleAddAbsence}
            data-testid="button-confirm"
          />
        </ButtonContainer>
        <AbsencesPageEmployeesList
          selectedEmployee={selectedEmployee}
          onSelectEmployee={onSelectEmployee}
          data-testid="absences-page-employees-list"
        />
      </OuterContainer>
    </>
  );
};

export default AttendancesRightView;
