export const isValidEmail = (email: string): boolean => {
  if (email === "") return true;

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

export const isValidUsername = (username: string): boolean => {
  if (username === "") return true;

  if (isValidEmail(username)) return true;

  const numberRegex = /^\d{6}$/;
  return numberRegex.test(username);
};
