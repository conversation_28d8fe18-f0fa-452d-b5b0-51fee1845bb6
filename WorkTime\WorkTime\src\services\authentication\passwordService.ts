import { PasswortStrengthType } from "../../models/Enums/PasswortStrengthType";

export const passwordStrengthCheck = (
  password: string
): PasswortStrengthType => {
  let score = -1;

  // Regular expression to match special characters
  var specialChars = /["!@#$%^&*()_+{}\[\]:;<>,.?~\-]/;

  // Check if password length is greater than or equal to 8
  if (password.length >= 8) {
    score++;
  }

  // Check if password has a number
  if (/\d/.test(password)) {
    score++;
  }

  // Check if password has a lowercase letter
  if (/[a-zа-я]/.test(password)) {
    score++;
  }

  // Check if password has an uppercase letter
  if (/[A-ZА-Я]/.test(password)) {
    score++;
  }

  // Check if password has a special character (Cyrillic or non-Cyrillic)
  if (specialChars.test(password)) {
    score++;
  }

  return score;
};
