import {
  <PERSON><PERSON><PERSON>,
  Mouse<PERSON>vent,
  useContext,
  useEffect,
  useState,
} from "react";
import Textbox from "../../components/Inputs/Textbox";
import Button from "../../components/Inputs/Button";
import { useAppDispatch } from "../../app/hooks";
import { onCompanyEdit } from "./companiesActions";
import MainWindowContainer from "../../components/MainWindowContainer";
import styled, { css, keyframes } from "styled-components";
import { shareCompany } from "../../services/companies/companiesService";
import upImage from "../../assets/images/button/up.png";
import { CompanyContext } from "./CompanyContext";
import UploadButton from "../../components/UploadButton/UploadButton";
import pencilImage from "../../assets/images/button/pencil.png";
import pencilDisableImage from "../../assets/images/button/pencilDisable.png";
import shareImage from "../../assets/images/button/share.png";
import shareDisableImage from "../../assets/images/button/shareDisable.png";
import Container from "../../components/Container";
import Dropdown from "../../components/Dropdown/Dropdown";
import { RoleDTO } from "../../models/DTOs/RoleDTO";
import { getRoles } from "../../services/authorization/authorizationService";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { isValidEmail } from "../../services/emailService";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const CompanyButtonsContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
`;

const LogoContainer = styled(Container)`
  width: 8rem;
  float: left;
  background-color: var(--upload-button-background-color);
  height: 8rem;
  border-radius: 4.5rem;
  margin-top: 1.5rem;
`;

const CompanyInfoContainer = styled(Container)`
  width: 65%;
  float: right;
`;

const StyledButton = styled(Button)`
  width: -webkit-fill-available;
  margin-top: 2rem;
`;

const CompanyButton = styled(Button)<{
  companyImage: string;
  companyHoverImage: string;
  companyDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--company-button-background-color);
  color: var(--company-button-color);
  font-size: 1.125rem;
  margin-left: 0.225rem;
  background-image: url(${(p) => p.companyImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  cursor: default;

  ${(p) => p.isDisable && Disable}

  ${(p) =>
    p.isDisable &&
    css`
      &:hover {
        background-image: url(${p.companyHoverImage});
        background-color: var(--company-button-background-color-hover);
        color: var(--company-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const ShareButton = styled(Button)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.5rem;
  width: 8rem;
  margin: 0;
  height: 2rem;
  padding: 0;
  background-color: #3893ef;
`;

const Wrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  padding: 0.5rem 1rem 0.5rem 1.5rem;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  &:last-child {
    border-radius: 0 0 2rem 2rem;
    padding-bottom: 2rem;
  }

  animation: ${({ isOpen }) =>
    isOpen
      ? css`
          ${fadeIn} 0.3s ease-in
        `
      : css`
          ${fadeOut} 0.3s ease-out
        `};
`;

const fadeIn = keyframes`
  from {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
    to {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.625rem;
    }
`;

const fadeOut = keyframes`
  from {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.188rem;
    }
    to {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
`;

const Disable = css<{
  companyDisabledImage: string;
}>`
  background-image: url(${(p) => p.companyDisabledImage});
  background-color: var(--company-button-background-color-disable);
  color: var(--company-button-color-disable);
`;

const HeaderWrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1rem 1rem 1rem 1.5rem;
  transition: 0.4s;
  background: var(--company-dropdown-opened-color);
  outline: none;
  cursor: pointer;
  &:first-child {
    border-radius: ${(props) =>
      props.isOpen ? "1.625rem 1.625rem 0 0" : "2rem"};
  }
`;

const HeaderImage = styled(Container)<{ isClicked: boolean }>`
  position: absolute;
  background-size: cover;
  height: 1rem;
  width: 1rem;
  right: 1.5rem;
  top: 40%;
  cursor: pointer;
  background-image: url(${upImage});
  transition-duration: 0.5s;
  ${({ isClicked }) =>
    !isClicked &&
    `transform:rotate(180deg);
    transition-duration: 0.5s;
    background-image: url(${upImage});
    top: 45%;
  `}
`;

const DropdownBody = styled(Dropdown.Body)`
  width: 100%;
  max-height: 30rem;
  overflow-y: auto;
  scrollbar-width: none;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track-color);
  }
`;

const EmailInputContainer = styled(Container)`
  position: relative;
  align-items: center;
  justify-content: center;
  width: 100%;
`;

const EmailInput = styled(Textbox)`
  height: 3.4rem;

  /* Target the InputField inside this styled Textbox */
  input {
    height: 3rem;
  }
`;

const EditCompany = () => {
  const dispatch = useAppDispatch();

  const { company, setCompany } = useContext(CompanyContext);
  const [newCompany, setNewCompany] = useState(company);
  const [email, setEmail] = useState("");
  const [editCompanyVisible, setEditCompanyVisible] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState(
    undefined as RoleDTO | undefined
  );

  const [roles, setRoles] = useState([] as RoleDTO[]);

  const handleIsOpened = (isOpen: boolean) => {
    setIsOpen(isOpen);
  };

  const handleRoleChange = (role: RoleDTO) => {
    setSelectedRole(role);
  };

  useEffect(() => {
    getRoles().then((response: RoleDTO[]) => {
      setRoles(response);
    });
  }, []);

  const handleCompanyChange = (e: ChangeEvent<HTMLInputElement>) => {
    setNewCompany({
      ...newCompany,
      [e.currentTarget.name]: e.currentTarget.value,
    });
  };

  const handleEditCompany = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    dispatch(
      onCompanyEdit({
        ...newCompany,
      })
    );

    setCompany(newCompany);
  };

  const handleEditCompanyVisible = () => {
    setEditCompanyVisible(true);
  };

  const handleShareCompanyVisible = () => {
    setEditCompanyVisible(false);
  };

  const handleShareCompanyChanged = (e: ChangeEvent<HTMLInputElement>) => {
    setEmail(e.currentTarget.value);
  };

  const handleShareCompany = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (!selectedRole) {
      return;
    }

    shareCompany(
      email,
      company.id,
      selectedRole.id,
      translate(selectedRole.name)
    );
    toast.success(translate("strCompanyInvitationSent"));
    setSelectedRole(undefined);
    setEmail("");
  };

  return (
    <MainContainer data-testid="main-container">
      <CompanyButtonsContainer data-testid="company-buttons-container">
        <CompanyButton
          data-testid="edit-company-button"
          label="Editing"
          companyImage={pencilImage}
          companyDisabledImage={pencilDisableImage}
          companyHoverImage={pencilDisableImage}
          isDisable={!editCompanyVisible}
          onClick={handleEditCompanyVisible}
        />
        <CompanyButton
          data-testid="share-company-button"
          label="Sharing"
          companyImage={shareImage}
          companyDisabledImage={shareDisableImage}
          companyHoverImage={shareDisableImage}
          onClick={handleShareCompanyVisible}
          isDisable={editCompanyVisible}
        />
      </CompanyButtonsContainer>
      {editCompanyVisible && (
        <>
          <LogoContainer data-testid="logo-container">
            <UploadButton data-testid="upload-button" />
          </LogoContainer>
          <CompanyInfoContainer data-testid="company-info-container">
            <Textbox
              data-testid="company-name-textbox"
              name="name"
              handleChange={handleCompanyChange}
              value={newCompany.name}
              label="Company Name"
            />
            <Textbox
              data-testid="company-bulstat-textbox"
              name="bulstat"
              handleChange={handleCompanyChange}
              value={newCompany.bulstat}
              label="EIK"
            />
            <Textbox
              data-testid="company-contact-name-textbox"
              name="contactName"
              handleChange={handleCompanyChange}
              value={newCompany.contactName}
              label="MOL"
            />
          </CompanyInfoContainer>
          <StyledButton
            data-testid="edit-button"
            label="Edit"
            onClick={handleEditCompany}
          />
        </>
      )}
      {!editCompanyVisible && (
        <>
          <Dropdown data-testid="role-dropdown" isOpened={handleIsOpened}>
            <Dropdown.Header data-testid="dropdown-header">
              <HeaderWrapper data-testid="header-wrapper" isOpen={isOpen}>
                <span
                  data-testid="selected-role-span"
                  style={{
                    color: selectedRole
                      ? "inherit"
                      : "var(--input-placeholder-color, #999)",
                  }}
                >
                  {selectedRole
                    ? translate(selectedRole.name)
                    : translate("Role")}
                </span>
              </HeaderWrapper>
              <HeaderImage
                data-testid="header-image"
                isClicked={isOpen}
              ></HeaderImage>
            </Dropdown.Header>
            <DropdownBody data-testid="dropdown-body">
              {roles.map((role) => (
                <Wrapper
                  data-testid={`role-wrapper-${role.name}`}
                  isOpen={isOpen}
                  key={role.name}
                  onClick={() => handleRoleChange(role)}
                >
                  {translate(role.name)}
                </Wrapper>
              ))}
            </DropdownBody>
          </Dropdown>
          <EmailInputContainer data-testid="email-input-container">
            <EmailInput
              data-testid="email-input"
              name="email"
              label="E mail"
              value={email}
              handleChange={handleShareCompanyChanged}
            />
            <ShareButton
              data-testid="invite-button"
              label="Invite"
              onClick={handleShareCompany}
              disabled={
                !selectedRole || !isValidEmail(email) || email.length === 0
              }
            />
          </EmailInputContainer>
        </>
      )}
    </MainContainer>
  );
};

export default EditCompany;
