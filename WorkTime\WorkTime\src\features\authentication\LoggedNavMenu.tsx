import { useContext, useEffect } from "react";
import styled from "styled-components";
import { useLocation } from "react-router-dom";
import { CompanyContext } from "../companies/CompanyContext";
import ProfileImage from "../../assets/images/menu/profile.png";
import Label from "../../components/Inputs/Label";
import Menu from "../Menu";
import { useMenu } from "../MenuContext";

const Image = styled.img`
  margin-left: 0.5rem;
  cursor: pointer;
`;

const CompanyLabel = styled(Label)`
  color: var(--company-name-label);
  cursor: pointer;
`;

const Wrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

interface Props {
  onProfileClick: () => void;
}

const LoggedNavMenu = ({ onProfileClick }: Props) => {
  const { company } = useContext(CompanyContext);
  const { isOpen, toggleMenu, changeView } = useMenu();
  const location = useLocation();

  useEffect(() => {}, [company]);

  const handleProfileClick = () => {
    changeView("profile", "other");
    onProfileClick();
  };

  const handleMenuToggle = () => {
    changeView("myCompanies", "companyLabel");
    toggleMenu();
  };

  return (
    <Wrapper data-testid="wrapper">
      {location.pathname !== "/" && (
        <CompanyLabel data-testid="company-label" onClick={handleMenuToggle}>
          {company.name ?? ""}
        </CompanyLabel>
      )}
      <Image
        data-testid="profile-image"
        onClick={handleProfileClick}
        src={ProfileImage}
      />

      <Menu
        data-testid="menu"
        isOpen={isOpen}
        onToggleMenu={handleMenuToggle}
      />
    </Wrapper>
  );
};

export default LoggedNavMenu;
