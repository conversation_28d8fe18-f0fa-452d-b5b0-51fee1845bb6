import { css, styled } from "styled-components";
import MainWindowContainer from "../../components/MainWindowContainer";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import planceImage from "../../assets/images/button/plane.png";
import planceDisableImage from "../../assets/images/button/planeDisabled.png";
import heartImage from "../../assets/images/button/heart.png";
import heartDisableImage from "../../assets/images/button/heartDisabled.png";
import { useEffect, useState } from "react";
import Combobox from "../../components/Combobox/Combobox";
import uploadWord from "../../assets/images/attendancies/uploadWord.png";
import Image from "../../components/Image";
import Label from "../../components/Inputs/Label";
import Datepicker from "../../components/Datepicker/Datepicker";

const MainContainer = styled(MainWindowContainer)`
  width: 100%;
  align-items: center;
`;

const AbsencesButtonsContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
`;

const AbsenceView = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const Row = styled(Container)`
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
`;

const ImageUpload = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const StyledLabel = styled(Label)`
  font-size: clamp(0.6em, 0.8vw, 1em);
  width: 45%;
  text-align: center;
`;

const AbsenceDetails = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 11rem;
  margin-left: 2em;
`;

const RequestButton = styled(Button)`
  margin-top: 2.1rem;
  width: 100%;
`;

const StyledButton = styled(Button)<{
  image: string;
  hoverImage: string;
  disabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--absence-button-background-color);
  color: var(--absence-button-color);
  font-size: 1.125rem;
  margin-left: 0.225rem;
  background-image: url(${(p) => p.image});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  cursor: default;

  ${(p) => p.isDisable && Disable}

  ${(p) =>
    p.isDisable &&
    css`
      &:hover {
        background-image: url(${p.hoverImage});
        background-color: var(--absence-button-background-color-hover);
        color: var(--absence-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const Disable = css<{
  disabledImage: string;
}>`
  background-image: url(${(p) => p.disabledImage});
  background-color: var(--absence-button-background-color-disable);
  color: var(--absence-button-color-disable);
`;

const formatDate = (date: Date) => {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
};

const Absence = () => {
  const options = ["Paid Leave", "Unpaid Leave"];

  //TO DO променливи, които да вземата начална, крайна дата и избрана опция от страничния прозорец
  const [startDate, setStartDate] = useState<string>(formatDate(new Date()));
  const [endDate, setEndDate] = useState<string>(formatDate(new Date()));
  const [selectedOption, setSelectedOption] = useState<string>(options[0]);
  const [absencesVisible, setAbsencesVisible] = useState(true);

  const handleSelectedChange = (newSelectedOption: string) => {
    setSelectedOption(newSelectedOption);
  };

  const handleAbsencesVisible = () => {
    setAbsencesVisible(true);
  };

  const handleSickNotesVisible = () => {
    setAbsencesVisible(false);
  };

  const handleStartDateSelect = (date: Date) => {
    setStartDate(formatDate(date));
  };

  const handleEndDateSelect = (date: Date) => {
    setEndDate(formatDate(date));
  };

  const handleSubmit = () => {
    if (absencesVisible) {
    }
  };

  return (
    <MainContainer data-testid="absence-main-container">
      <AbsencesButtonsContainer data-testid="absences-buttons-container">
        <StyledButton
          data-testid="absences-button"
          label="Absences"
          image={planceImage}
          disabledImage={planceDisableImage}
          hoverImage={planceDisableImage}
          isDisable={!absencesVisible}
          onClick={handleAbsencesVisible}
        />
        <StyledButton
          data-testid="sick-leave-button"
          label="Sick Leave"
          image={heartImage}
          disabledImage={heartDisableImage}
          hoverImage={heartDisableImage}
          onClick={handleSickNotesVisible}
          isDisable={absencesVisible}
        />
      </AbsencesButtonsContainer>
      <AbsenceView data-testid="absence-view">
        <Row data-testid="absence-row">
          <ImageUpload data-testid="image-upload-container">
            <Image
              data-testid="upload-word-image"
              src={uploadWord}
              size="large"
              alt="word file"
            ></Image>
            <StyledLabel
              data-testid="upload-file-label"
              children="Upload File"
            ></StyledLabel>
          </ImageUpload>
          <AbsenceDetails data-testid="absence-details">
            <Datepicker
              data-testid="start-date-picker"
              onSelectDate={handleStartDateSelect}
              label="Start Date"
            ></Datepicker>
            <Datepicker
              data-testid="end-date-picker"
              onSelectDate={handleEndDateSelect}
              label="End Date"
            ></Datepicker>

            <Combobox
              data-testid="absence-type-combobox"
              options={options}
              onChange={handleSelectedChange}
              isHidden={!absencesVisible}
            />
          </AbsenceDetails>
        </Row>
        <RequestButton
          data-testid="request-button"
          label={absencesVisible ? "Send Request" : "Upload"}
          onClick={handleSubmit}
        ></RequestButton>
      </AbsenceView>
    </MainContainer>
  );
};

export default Absence;
