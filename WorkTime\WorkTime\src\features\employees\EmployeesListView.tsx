import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import Table, { ColumnDefinitionType } from "../../components/Table/Table";
import Avatar from "../../components/Table/Avatar";
import MainWindowContainer from "../../components/MainWindowContainer";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import {
  onEmployeePayrollsLoaded,
  selectEmployeePayrolls,
} from "../payroll/employeePayrollActions";
import { useNavigate } from "react-router-dom";
import TableHeaderExtended from "./TableHeaderExtended";
import { IEntity } from "../../models/DTOs/IEntity";
import {
  onStructureLevelsLoaded,
  selectStructureLevels,
} from "../company-structure/companyStructureActions";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { useEnums } from "../EnumContext";
import { NomenclatureDTO } from "../../models/DTOs/nomenclatures/NomenclatureDTO";
import { Direction } from "../../components/Table/Direction";

export interface EmployeeListView extends IEntity {
  avatar: React.ReactNode;
  id: string;
  egn: string;
  fullName: string;
  email: string;
  position: string;
  departmentId: string;
  department: string;
  typeOfAppointment: NomenclatureDTO | undefined;
  category: NomenclatureDTO | undefined;
  number: string;
}

const EmployeesListView = () => {
  const dispatch = useAppDispatch();
  const { payrollCategories, appointmentTypes } = useEnums();
  const employeePayrolls = useAppSelector(selectEmployeePayrolls)
    .employeePayrolls as EmployeePayrollDTO[];

  const structureLevels = useAppSelector(selectStructureLevels).structureLevels;
  const [sort, setSort] = useState<{ [key: string]: number }>({});
  const [departmentsOptions, setDepartmentsOptions] = useState<
    { identifier: string; name: string }[]
  >([]);
  const [mappedEmployees, setMappedEmployees] = useState<EmployeeListView[]>(
    []
  );

  const [filteredEmployees, setFilteredEmployees] = useState<
    EmployeeListView[]
  >([]);
  const navigate = useNavigate();

  const [filters, setFilters] = useState({
    personalData: "",
    position: "",
    departments: [] as string[],
    categories: [] as string[],
    typeOfAppointment: [] as string[],
  });

  const categoriesOptions = payrollCategories.map((category) => ({
    identifier: category.identifier,
    name: category.name,
    description: category.name,
  }));

  const typesOfAppointmentOptions = appointmentTypes.map((type) => ({
    identifier: type.identifier,
    name: type.name,
    description: type.name,
  }));

  const rowColumns: ColumnDefinitionType<
    EmployeeListView,
    keyof EmployeeListView
  >[] = [
    { key: "avatar", value: "" },
    { key: "fullName", value: "Име" },
    { key: "egn", value: "ЕГН" },
    { key: "position", value: "Длъжност" },
    { key: "department", value: "Отдел" },
    { key: "number", value: "Номер" },
    { key: "typeOfAppointment", value: "Тип на назначение" },
  ];

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
    dispatch(onEmployeePayrollsLoaded(companyId));
    dispatch(onStructureLevelsLoaded(companyId));
  }, [dispatch]);

  useEffect(() => {
    if (structureLevels?.[0]?.childStructureLevels) {
      setDepartmentsOptions(
        structureLevels[0].childStructureLevels.map((level) => ({
          identifier: level.id,
          name: level.name,
        }))
      );
    }
  }, [structureLevels]);

  useEffect(() => {
    const mapped = mapToEmployeeListView(employeePayrolls);
    setMappedEmployees(mapped);
    onSortChange("fullName", Direction.Ascending);
  }, [departmentsOptions, employeePayrolls]);

  useEffect(() => {
    const filteredEmployees = filterEmployees(mappedEmployees);
    const sortedEmployees = sortEmployees(filteredEmployees);
    setFilteredEmployees(sortedEmployees);
    console.log(sort);
  }, [filters, sort, mappedEmployees]);

  const onTextFilterChange = (columnKey: string, searchText: string) => {
    setFilters((prev) => ({
      ...prev,
      [columnKey]: searchText,
    }));
  };

  const onFilterChange = (
    filterType: "departments" | "categories" | "typeOfAppointment",
    selected: string[]
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: selected,
    }));
  };

  const onSortChange = (columnKey: string, direction: Direction) => {
    const directionValue = direction === Direction.Ascending ? 1 : -1;
    setSort({ [columnKey]: directionValue });
  };

  const onRowClick = (id: string) => {
    navigate(`${id}`);
  };

  const mapToEmployeeListView = (
    employees: EmployeePayrollDTO[]
  ): EmployeeListView[] => {
    const employeeListView = employees.map((employeePayroll) => {
      const departmentInfo = departmentsOptions.find(
        (option) => option.identifier === employeePayroll.structureId
      );

      return {
        id: employeePayroll.employeeGuid,
        workTimeId: employeePayroll.employeeGuid,
        avatar: (
          <Avatar
            name={employeePayroll.firstName}
            photo={""}
            size={2}
            data-testid={`employee-avatar-${employeePayroll.employeeGuid}`}
          />
        ),
        fullName: [
          employeePayroll.firstName,
          employeePayroll.secondName,
          employeePayroll.lastName,
        ]
          .filter((name) => name != null)
          .join(" "),
        egn: employeePayroll.egn,
        email: employeePayroll.email,
        position: employeePayroll.positionName,
        departmentId: departmentInfo?.identifier || "",
        department: departmentInfo?.name || "",
        contractType: employeePayroll.contractNumber,
        category: employeePayroll.category,
        typeOfAppointment: employeePayroll.typeOfAppointment,
        number: employeePayroll.contractNumber || "",
      };
    });

    return employeeListView;
  };

  const filterEmployees = (
    employees: EmployeeListView[]
  ): EmployeeListView[] => {
    const searchWords = filters.personalData
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 0);

    return employees.filter((employee) => {
      const personalDataMatch =
        !filters.personalData ||
        matchesPersonalDataFilter(employee, searchWords);

      const positionMatch =
        !filters.position ||
        (employee.position &&
          employee.position
            ?.trim()
            .toLowerCase()
            .includes(filters.position.trim().toLowerCase()));

      const departmentMatch =
        filters.departments.length === 0 ||
        filters.departments.includes(
          departmentsOptions
            .find((option) => option.identifier === employee.departmentId)
            ?.identifier?.toString() || ""
        );

      const typeOfAppointmentMatch =
        filters.typeOfAppointment.length === 0 ||
        filters.typeOfAppointment.includes(
          typesOfAppointmentOptions
            .find(
              (option) =>
                option.identifier === employee.typeOfAppointment?.identifier
            )
            ?.identifier?.toString() || ""
        );

      const categoryMatch =
        filters.categories.length === 0 ||
        filters.categories.includes(
          categoriesOptions
            .find(
              (option) => option.identifier === employee.category?.identifier
            )
            ?.identifier?.toString() || ""
        );

      return (
        personalDataMatch &&
        positionMatch &&
        departmentMatch &&
        typeOfAppointmentMatch &&
        categoryMatch
      );
    });
  };

  const matchesPersonalDataFilter = (
    employee: EmployeeListView,
    searchWords: string[]
  ) => {
    if (searchWords.length == 0) return false;

    const employeeFields = [
      employee.fullName,
      employee.egn,
      employee.email,
      employee.position,
      employee.department,
      employee.typeOfAppointment?.description,
      employee.category?.description,
    ].map((field) => field?.toLowerCase() || "");

    return searchWords.every((word) =>
      employeeFields.some((field) => field.includes(word))
    );
  };

  const sortEmployees = (employees: EmployeeListView[]): EmployeeListView[] => {
    let sortKey = Object.keys(sort)[0];
    const sortDirection = sort[sortKey];

    if (!sortKey) return employees;

    if (sortKey === "personalData") {
      sortKey = "fullName";
    }

    return [...employees].sort((a, b) => {
      const valA = (a as any)[sortKey]?.toLowerCase() || "";
      const valB = (b as any)[sortKey]?.toLowerCase() || "";
      return valA < valB
        ? -1 * sortDirection
        : valA > valB
        ? 1 * sortDirection
        : 0;
    });
  };

  return (
    <MainWindowContainer data-testid="employees-list-container">
      <Table
        data-testid="employees-table"
        onRowClick={onRowClick}
        data={filteredEmployees}
        columns={rowColumns}
        onSortChange={
          onSortChange as (columnKey: string, direction: Direction) => void
        }
        headerComponent={
          <TableHeaderExtended
            data-testid="table-header-extended"
            departmentOptions={departmentsOptions}
            categoryOptions={categoriesOptions}
            typeOfAppointmentOptions={typesOfAppointmentOptions}
            selectedDepartments={filters.departments}
            selectedCategories={filters.categories}
            selectedTypeOfAppointment={filters.typeOfAppointment}
            onDepartmentChange={(selected) =>
              onFilterChange("departments", selected)
            }
            onCategoryChange={(selected) =>
              onFilterChange("categories", selected)
            }
            onTypeOfAppointmentChange={(selected) =>
              onFilterChange("typeOfAppointment", selected)
            }
            onTextFilterChange={onTextFilterChange}
          />
        }
      />
    </MainWindowContainer>
  );
};

export default EmployeesListView;
