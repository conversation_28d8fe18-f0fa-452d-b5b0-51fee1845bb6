import { Action, Reducer } from "redux";
import { AppThunk, RootState } from "../../app/store";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";
import { AddPayrollRequest } from "../../models/Requests/AddPayrollRequest";
import {
  authenticatedGet,
  authenticatedPost,
  authenticatedDelete,
} from "../../services/connectionService";

interface PayrollsState {
  payrolls: PayrollDTO[];
}

interface AddPayrollAction {
  type: "ADD_PAYROLL";
  payroll: PayrollDTO;
}

interface LoadPayrollsAction {
  type: "LOAD_PAYROLLS";
  payrolls: PayrollDTO[];
}

interface DeletePayrollAction {
  type: "DELETE_PAYROLL";
  payrollId: string;
}

type KnownActions = AddPayrollAction | LoadPayrollsAction | DeletePayrollAction;

const addPayrollAction = (payroll: PayrollDTO): AddPayrollAction => ({
  type: "ADD_PAYROLL",
  payroll,
});

const loadPayrollsAction = (payrolls: PayrollDTO[]): LoadPayrollsAction => ({
  type: "LOAD_PAYROLLS",
  payrolls,
});

const deletePayrollAction = (payrollId: string): DeletePayrollAction => ({
  type: "DELETE_PAYROLL",
  payrollId,
});

export const actionCreators = {
  onPayrollSaved: (payroll: PayrollDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const addPayrollRequest = { payrollDTO: payroll } as AddPayrollRequest;
      authenticatedPost<PayrollDTO>(
        "payrolls/add-payroll",
        addPayrollRequest
      ).then((payroll) => {
        dispatch(addPayrollAction(payroll));
      });
    };
  },
  onPayrollsLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<PayrollDTO[]>(
        `payrolls/load-payrolls?companyId=${companyId}`
      ).then((payrolls) => {
        dispatch(loadPayrollsAction(payrolls));
      });
    };
  },
  onPayrollDelete: (payrollId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedDelete("payrolls/delete-payroll?PayrollId=", payrollId)
        .then(() => {
          dispatch(deletePayrollAction(payrollId));
        })
        .catch((er) => {
          alert(er);
        });
    };
  },
};
export const { onPayrollSaved, onPayrollsLoaded, onPayrollDelete } =
  actionCreators;

const initialState = {
  payrolls: [],
} as PayrollsState;

export const reducer: Reducer<PayrollsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "ADD_PAYROLL":
      return {
        ...state,
        payrolls: [...state.payrolls, incomingAction.payroll],
      };
    case "LOAD_PAYROLLS":
      return {
        ...state,
        payrolls: [...incomingAction.payrolls],
      };
    case "DELETE_PAYROLL":
      const payrollId = incomingAction.payrollId;
      return {
        ...state,
        payrolls: [...state.payrolls.filter((p) => p.workTimeId !== payrollId)],
      };
    default:
      return state;
  }
};

export const selectPayrolls = (state: RootState) => state.payrolls;
