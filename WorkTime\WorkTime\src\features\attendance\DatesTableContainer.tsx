import React, { useEffect, useState } from "react";
import DatesTableView, {
  DayInfo,
} from "../../components/CalendarComponent/DatesTableView";
import {
  AlignmentPosition,
  EmployeeInfo,
} from "../../components/CalendarComponent/types/types";
import { selectLeaves, onLeavesLoaded } from "./attendanceActions";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { useFilteredEmployees, Employee } from "./useFilteredEmployees";

interface LocalEmployee {
  id: string;
  name: string;
  leaveStart: string;
  leaveEnd: string;
  color: string;
}

interface EmployeeRowPosition {
  [week: number]: { [employeeId: string]: number };
}

interface DatesTableContainerProps {
  selectedEmployee: Employee | null;
  hoveredEmployee?: Employee | null;
}

const DatesTableContainer: React.FC<DatesTableContainerProps> = ({
  selectedEmployee,
  hoveredEmployee,
}) => {
  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls);
  const leaves = useAppSelector(selectLeaves);
  const [selectionStart, setSelectionStart] = useState<number | null>(null);

  const [currentDate, setCurrentDate] = useState<number>(new Date().getDate());
  const [currentYear, setCurrentYear] = useState<number>(
    new Date().getFullYear()
  );
  const [currentMonth, setCurrentMonth] = useState<number>(
    new Date().getMonth()
  );

  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [employeeRowPositions, setEmployeeRowPositions] =
    useState<EmployeeRowPosition>({});
  const [combinedDays, setCombinedDays] = useState<DayInfo[]>([]);
  const [positionUpdateFlag, setPositionUpdateFlag] = useState<boolean>(false);

  useEffect(() => {
    const fullDate = new Date();

    setCurrentDate(fullDate.getDate());
    setCurrentYear(fullDate.getFullYear());
    setCurrentMonth(fullDate.getMonth());
  }, []);

  useEffect(() => {
    console.log("Selected Month:", selectedMonth);
    console.log("Selected Year:", selectedYear);

    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
    const date = new Date(selectedYear, selectedMonth, 1);
    console.log("Date for Leaves:", date);

    dispatch(onLeavesLoaded(date, companyId));
  }, [dispatch, selectedMonth, selectedYear]);

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    if (!payrolls.payrolls || payrolls.payrolls.length === 0) {
      dispatch(onPayrollsLoaded(companyId));
    }
  }, [dispatch, payrolls]);

  const filteredEmployees = useFilteredEmployees(
    payrolls.payrolls,
    leaves,
    selectedMonth,
    selectedYear
  );

  useEffect(() => {
    const updatedPositions = fillEmployeeRowPositions(employeeRowPositions);
    const adjustedPositions = adjustPositions(updatedPositions);
    const updatedPositionsForAllDays =
      fillEmployeeRowPositions(adjustedPositions);
    setEmployeeRowPositions(updatedPositionsForAllDays);
    updateMissingEmployeesForAllDays(updatedPositionsForAllDays);
  }, [positionUpdateFlag, filteredEmployees]);

  useEffect(() => {
    const newDays = calculateDays(selectedMonth, selectedYear);
    setCombinedDays(newDays);
    setEmployeeRowPositions({});
    setPositionUpdateFlag(!positionUpdateFlag);

    console.log("New Days Calculated:", newDays);
  }, [selectedMonth, selectedYear]);

  const findMonthNumber = (type: string) => {
    let currMonthOffset: number;
    if (type === "prevMonth") currMonthOffset = -1;
    else if (type === "currentMonth") currMonthOffset = 0;
    else currMonthOffset = 1;
    return selectedMonth + currMonthOffset;
  };

  const updateMissingEmployeesForAllDays = (
    adjustedPositions: EmployeeRowPosition
  ) => {
    const updatedDays = combinedDays.map((day) => ({
      ...day,
      missingEmployees: [] as EmployeeInfo[],
    }));

    updatedDays.forEach((dayInfo, index) => {
      const currMonth = findMonthNumber(dayInfo.type);
      const dayDate = new Date(selectedYear, currMonth, dayInfo.dayNumber);
      const weekNumber = Math.floor(index / 7);
      const currentEmployeeRowPosition = adjustedPositions[weekNumber] || {};

      filteredEmployees.forEach((employee) => {
        if (isEmployeeMissingOnDay(employee, dayDate)) {
          const leaveStart = new Date(employee.leaveStart);
          const leaveEnd = new Date(employee.leaveEnd);
          dayDate.setHours(0, 0, 0, 0);

          let positionAlignment: AlignmentPosition = AlignmentPosition.Center;
          if (dayDate.getTime() === leaveStart.getTime()) {
            positionAlignment = AlignmentPosition.Left;
          } else if (dayDate.getTime() === leaveEnd.getTime()) {
            positionAlignment = AlignmentPosition.Right;
          }

          dayInfo.missingEmployees = [
            ...dayInfo.missingEmployees,
            {
              id: employee.payrollId,
              name: employee.name,
              color: employee.color,
              row: currentEmployeeRowPosition[employee.payrollId],
              positonRounding: positionAlignment,
            },
          ];
        }
      });
    });
    setCombinedDays(updatedDays);
    console.log("Updated Days with Missing Employees:", updatedDays);
  };

  const fillEmployeeRowPositions = (adjustedPositions: EmployeeRowPosition) => {
    combinedDays.forEach((dayInfo, index) => {
      const currMonth = findMonthNumber(dayInfo.type);
      const dayDate = new Date(selectedYear, currMonth, dayInfo.dayNumber);
      const weekNumber = Math.floor(index / 7);
      const currentEmployeeRowPosition = adjustedPositions[weekNumber] || {};
      adjustedPositions[weekNumber] = currentEmployeeRowPosition;

      filteredEmployees.forEach((employee) => {
        console.log("Employee:", employee.name);
        if (isEmployeeMissingOnDay(employee, dayDate)) {
          if (!(employee.payrollId in currentEmployeeRowPosition)) {
            adjustedPositions[weekNumber][employee.payrollId] =
              updateRowPositionsForWeek(
                weekNumber,
                employee.payrollId,
                adjustedPositions
              );
          }
        }
      });
    });
    return adjustedPositions;
  };

  const adjustPositions = (updatedEmployees: EmployeeRowPosition) => {
    Object.entries(updatedEmployees).forEach(([week, positions]) => {
      const sortedEntries = Object.entries(positions).sort(
        (a, b) => (a[1] as unknown as number) - (b[1] as unknown as number)
      );
      updatedEmployees[Number(week)] = sortedEntries.reduce<{
        [key: string]: number;
      }>((acc, [employee], index) => {
        acc[employee] = index + 1;
        return acc;
      }, {});
    });
    return updatedEmployees;
  };

  const isEmployeeMissingOnDay = (
    employee: Employee,
    dayDate: Date
  ): boolean => {
    const leaveStart = new Date(employee.leaveStart);
    const leaveEnd = new Date(employee.leaveEnd);
    return dayDate >= leaveStart && dayDate <= leaveEnd;
  };

  const updateRowPositionsForWeek = (
    weekNumber: number,
    newEmployee: string,
    adjustedPositions: EmployeeRowPosition
  ) => {
    const weekPositions = adjustedPositions[weekNumber] || {};
    const previousWeekPositions = adjustedPositions[weekNumber - 1] || {};
    return (
      previousWeekPositions[newEmployee] ||
      Math.max(0, ...Object.values(weekPositions)) + 1
    );
  };

  const createDayInfo = (dayNumber: number, type: string) => ({
    dayNumber,
    type,
    missingEmployees: [] as EmployeeInfo[],
  });

  const calculateDays = (selectedMonth: number, selectedYear: number) => {
    const daysInMonth = (month: number, year: number) =>
      new Date(year, month + 1, 0).getDate();

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1).getDay();
    const numDaysCurrentMonth = daysInMonth(selectedMonth, selectedYear);
    const adjustDayIndex = (dayIndex: number) =>
      dayIndex === 0 ? 7 : dayIndex;
    const prevMonthDisplayDays = adjustDayIndex(firstDayOfMonth) - 1;

    const prevMonthDays = Array.from(
      { length: prevMonthDisplayDays },
      (_, i) =>
        daysInMonth(selectedMonth - 1, selectedYear) -
        prevMonthDisplayDays +
        i +
        1
    );

    const currentMonthDays = Array.from(
      { length: numDaysCurrentMonth },
      (_, i) => i + 1
    );

    const totalDays = prevMonthDisplayDays + numDaysCurrentMonth;
    const nextMonthDisplayDays = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);

    const nextMonthDays = Array.from(
      { length: nextMonthDisplayDays },
      (_, i) => i + 1
    );

    return [
      ...prevMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "prevMonth")
      ),
      ...currentMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "currentMonth")
      ),
      ...nextMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "nextMonth")
      ),
    ] as DayInfo[];
  };

  const goToPrevMonth = () => {
    let newMonth = selectedMonth - 1;
    let newYear = selectedYear;
    if (newMonth < 0) {
      newMonth = 11;
      newYear -= 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToNextMonth = () => {
    let newMonth = selectedMonth + 1;
    let newYear = selectedYear;
    if (newMonth > 11) {
      newMonth = 0;
      newYear += 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const isCurrentDay = (day: number) => {
    return (
      selectedMonth === currentMonth &&
      selectedYear === currentYear &&
      day === currentDate
    );
  };

  return (
    <DatesTableView
      data-testid="dates-table-view"
      selectedMonth={selectedMonth}
      selectedYear={selectedYear}
      days={combinedDays}
      employeeRowPositions={employeeRowPositions}
      onPrevMonth={goToPrevMonth}
      onNextMonth={goToNextMonth}
      isCurrentDay={isCurrentDay}
      selectedEmployeeId={selectedEmployee?.payrollId}
      hoveredEmployeeId={hoveredEmployee?.payrollId}
    />
  );
};

export default DatesTableContainer;
