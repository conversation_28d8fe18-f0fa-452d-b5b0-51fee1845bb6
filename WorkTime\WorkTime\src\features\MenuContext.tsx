import { createContext, ReactNode, useContext, useState, useRef } from "react";

interface MenuContextProps {
  isOpen: boolean;
  toggleMenu: () => void;
  closeMenu: () => void;
  changeView: (
    activeView: string,
    openedFrom?: "companyLabel" | "associates" | "other"
  ) => void;
  activeView: string;
  currentPage: string;
  menuOpenedFrom: "companyLabel" | "associates" | "other";
}

const MenuContext = createContext<MenuContextProps>({
  isOpen: false,
  toggleMenu: () => {},
  closeMenu: () => {},
  changeView: () => {},
  activeView: "myCompanies",
  currentPage: location.pathname,
  menuOpenedFrom: "other",
});

export const useMenu = () => {
  return useContext(MenuContext);
};

interface MenuProviderProps {
  children: ReactNode;
}

export const MenuProvider = ({ children }: MenuProviderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const closeTimeoutRef = useRef<NodeJS.Timeout>();

  const [activeView, setActiveView] = useState("myCompanies");
  const [currentPage, setCurrentPage] = useState(location.pathname);
  const [menuOpenedFrom, setMenuOpenedFrom] = useState<
    "companyLabel" | "other" | "associates"
  >("other");

  const changeView = (
    activeView: string,
    openedFrom: "companyLabel" | "associates" | "other" = "other"
  ) => {
    setActiveView(activeView);
    setMenuOpenedFrom(openedFrom);
  };

  const toggleMenu = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = undefined;
    }
    setIsOpen((prevState: boolean) => !prevState);
    setCurrentPage(location.pathname);
  };

  const closeMenu = () => {
    setIsOpen(false);
    closeTimeoutRef.current = setTimeout(() => {
      changeView("profile", "other");
    }, 400);
  };

  const contextValue = {
    isOpen,
    toggleMenu,
    closeMenu,
    changeView,
    activeView,
    currentPage,
    menuOpenedFrom,
  };

  return (
    <MenuContext.Provider value={contextValue}>{children}</MenuContext.Provider>
  );
};
