import React, { useState } from "react";
import { styled } from "styled-components";
import Container from "../../components/Container";
import MainWindowContainer from "../../components/MainWindowContainer";
import AttendancesRightView from "./AttendancesRightView";
import DatesTableContainer from "./DatesTableContainer";
import { Employee } from "./useFilteredEmployees";

const FileContainer = styled(Container)`
  display: flex;
  z-index: 1;

  @media (max-width: 950px) {
    flex-direction: column;
    align-items: center;
    top: 0;
  }
`;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  top: 5em;
  margin: 0 1em 0 1em;

  @media (max-width: 950px) {
    order: 1;
    width: auto;
  }
`;

const RightContainer = styled(Container)`
  display: block;
  margin: 0 3em;
  border-radius: 2.2rem;

  @media (max-width: 950px) {
    order: 2;
    margin: 6em 0em;
  }
`;

const Attendance: React.FC = () => {
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );

  const [hoveredEmployee, setHoveredEmployee] = useState<Employee | null>(null);

  return (
    <MainWindowContainer data-testid="main-window-container">
      <FileContainer data-testid="file-container">
        <LeftContainer data-testid="left-container">
          <DatesTableContainer
            selectedEmployee={selectedEmployee}
            hoveredEmployee={hoveredEmployee}
            data-testid="dates-table-container"
          />
        </LeftContainer>
        <RightContainer data-testid="right-container">
          <AttendancesRightView
            selectedEmployee={selectedEmployee}
            onSelectEmployee={setSelectedEmployee}
            hoveredEmployee={hoveredEmployee}
            onEmployeeHover={setHoveredEmployee}
          />
        </RightContainer>
      </FileContainer>
    </MainWindowContainer>
  );
};

export default Attendance;
