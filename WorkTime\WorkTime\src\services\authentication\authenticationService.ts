import {
  LOCAL_STORAGE_ACCESS_TOKEN,
  LOCAL_STORAGE_COMPANY_ID,
  LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID,
  LOCAL_STORAGE_HAS_SIGNED_IN,
  LOCAL_STORAGE_REFRESH_TOKEN,
  LOCAL_STORAGE_USER_EMAIL,
  LOCAL_STORAGE_USER_ID,
  LOCAL_STORAGE_WORKTIME_ROLE_NAME,
} from "../../constants/local-storage-constants";
import { User } from "../../features/authentication/AuthContext";
import { LoginUserDTO } from "../../models/DTOs/LoginUserDTO";
import { RegisterUserDTO } from "../../models/DTOs/RegisterUserDTO";
import { SenderaUserDTO } from "../../models/DTOs/SenderaUserDTO";
import { UserDTO } from "../../models/DTOs/users/UserDTO";
import { ChangeForgottenPasswordRequest } from "../../models/Requests/ChangeForgottenPasswordRequest";
import {
  authenticatedGet,
  authenticatedPost,
  getResponse,
  post,
} from "../connectionService";

function getBasicAuthenticationToken(email: string, password: string) {
  return "Basic " + window.btoa(email + ":" + password);
}

export const registration = async (registerDTO: RegisterUserDTO) => {
  return await post(`user-registration/registration`, registerDTO);
};

export const confirmEmail = async (userId: string, code: string) => {
  return await post(`user-registration/confirm-email`, { userId, code });
};

export const getSenderaUser = async () => {
  return await authenticatedGet<SenderaUserDTO>(
    `user-registration/sendera-user`
  );
};

export const logout = () => {
  localStorage.removeItem(LOCAL_STORAGE_USER_EMAIL);
  localStorage.removeItem(LOCAL_STORAGE_ACCESS_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_REFRESH_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_HAS_SIGNED_IN);
  localStorage.removeItem(LOCAL_STORAGE_COMPANY_ID);
  localStorage.removeItem(LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID);
  localStorage.removeItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME);

  authenticatedPost(`sso/logout`);
};

export const login = async (
  loginDTO: LoginUserDTO,
  rememberMeChecked: boolean
) => {
  const authToken = getBasicAuthenticationToken(
    loginDTO.email,
    loginDTO.password
  );

  const response = await getResponse(`sso/try-login`, {
    Authorization: authToken,
  });

  if (response.status !== 200) {
    return undefined;
  }

  const user = (await response.json()) as UserDTO;

  localStorage.setItem(
    LOCAL_STORAGE_HAS_SIGNED_IN,
    user.hasSignedIn.toString()
  );
  localStorage.setItem(LOCAL_STORAGE_USER_ID, user.id);
  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, loginDTO.email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  if (rememberMeChecked) {
    localStorage.setItem(
      LOCAL_STORAGE_REFRESH_TOKEN,
      response.headers.get("Refresh-token")
    );
  }

  return user;
};

export const facebookLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-facebook-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const microsoftLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-microsoft-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const googleLogin = async (
  token?: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-google-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();
  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const isAuthenticated = () => {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN);
  return accessToken !== undefined && accessToken !== null;
};

export const initUser = (): User => {
  const email = localStorage.getItem(LOCAL_STORAGE_USER_EMAIL);
  if (isAuthenticated() && email) {
    const hasSignedIn = localStorage.getItem(LOCAL_STORAGE_HAS_SIGNED_IN);
    const workTimeRoleName = localStorage.getItem(
      LOCAL_STORAGE_WORKTIME_ROLE_NAME
    );
    const employeeId = localStorage.getItem(LOCAL_STORAGE_USER_ID);

    if (hasSignedIn) {
      return {
        email: email,
        hasSignedIn: hasSignedIn === "true",
        workTimeRoleName: workTimeRoleName ?? undefined,
        userId: employeeId ?? undefined,
      };
    }

    return {
      email: email,
      hasSignedIn: false,
      workTimeRoleName: workTimeRoleName ?? undefined,
      userId: undefined,
    };
  }

  return {
    email: undefined,
    hasSignedIn: false,
    workTimeRoleName: undefined,
    userId: undefined,
  };
};

export const changeForgottenPassword = async (
  changeForgottenPassword: ChangeForgottenPasswordRequest
) => {
  return await post<boolean>(
    "sso/change-forgotten-password",
    changeForgottenPassword
  );
};
