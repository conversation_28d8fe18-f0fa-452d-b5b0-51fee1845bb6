import { useMemo } from "react";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";
import { AbsencesAndHospitalsDTO } from "../../models/DTOs/attendance/AbsencesAndHospitalsDTO";

interface Employee {
  payrollId: string;
  name: string;
  leaveStart: string;
  leaveEnd: string;
  color: string;
}

const filterEmployeesWithLeaves = (
  payrolls: PayrollDTO[],
  leaves: AbsencesAndHospitalsDTO[],
  firstDayOfMonth: Date,
  lastDayOfMonth: Date
): PayrollDTO[] => {
  return payrolls.filter((emp: PayrollDTO) => {
    const empLeaves = leaves.filter(
      (leave: AbsencesAndHospitalsDTO) => leave.payrollId === emp.workTimeId
    );
    return empLeaves.some((leave: AbsencesAndHospitalsDTO) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);
      
      return leaveStart <= lastDayOfMonth && leaveEnd >= firstDayOfMonth;
    });
  });
};

const mapToEmployee = (emp: PayrollDTO, leaves: AbsencesAndHospitalsDTO[]): Employee => {
  const empLeaves = leaves.filter(
    (leave: AbsencesAndHospitalsDTO) => leave.payrollId === emp.workTimeId
  );
  const leave = empLeaves[0] || {};
  
  return {
    payrollId: emp.workTimeId,
    name: `${emp.employee.firstName || ''} ${emp.employee.lastName || ''}`,
    leaveStart: leave.fromDate || '',
    leaveEnd: leave.toDate || '',
    color: "#032",
  };
};

export const useFilteredEmployees = (
  payrolls: PayrollDTO[],
  leaves: AbsencesAndHospitalsDTO[],
  selectedMonth: number,
  selectedYear: number
): Employee[] => {
  return useMemo(() => {
    if (!payrolls || payrolls.length === 0 || !leaves || leaves.length === 0) {
        return [];
    }

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1);
    const lastDayOfMonth = new Date(selectedYear, selectedMonth + 1, 0);

    const filteredPayrolls = filterEmployeesWithLeaves(payrolls, leaves, firstDayOfMonth, lastDayOfMonth);
   
    const employees = filteredPayrolls.map(emp => mapToEmployee(emp, leaves));

    return employees;
  }, [payrolls, leaves, selectedMonth, selectedYear]);
};

export type { Employee }; 