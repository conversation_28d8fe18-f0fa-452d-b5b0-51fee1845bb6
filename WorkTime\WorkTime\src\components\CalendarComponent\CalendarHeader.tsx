import React from "react";
import styled from "styled-components";
import { ArrowButton } from "./ArrowButton";
import { months } from "./constants/Names";
import Label from "../Inputs/Label";
import Container from "../Container";

interface CalendarHeaderProps {
  selectedMonth: number;
  selectedYear: number;
  goToPrevMonth: () => void;
  goToNextMonth: () => void;
}

const HeaderContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  font-size: 1.3em;
  width: 100%;
  height: clamp(1em, 3vw, 2em);
  padding: 1em 0.3em 1em 0.6em;

  border-radius: 1.8em 1.8em 0 0;
  background-color: var(--datepicker-header-background-color);
  cursor: pointer;
  user-select: none;
`;

const LabelCentered = styled(Label)<{ fontSize: number }>`
  text-align: center;
  width: 100%;
  height: clamp(1em, 1.5vw, 1.5em);
  padding-bottom: 0.1em;
  font: Segoe UI;
  font-size: ${(props) => (props.fontSize ? `${props.fontSize}em` : "1.2em")};
  color: var(--datepicker-header-font-color);
`;

export const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  selectedMonth,
  selectedYear,
  goToPrevMonth,
  goToNextMonth,
}) => {
  return (
    <HeaderContainer data-testid="calendar-header">
      <ArrowButton direction="left" onClick={goToPrevMonth} />
      <LabelCentered fontSize={1.4} data-testid="year-label">
        {selectedYear.toString()}
      </LabelCentered>
      <LabelCentered fontSize={1.4} data-testid="month-label">
        {months[selectedMonth]}
      </LabelCentered>
      <ArrowButton direction="right" onClick={goToNextMonth} />
    </HeaderContainer>
  );
};
