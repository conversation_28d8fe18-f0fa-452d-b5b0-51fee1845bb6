import { ChangeEvent, MouseEvent, useState } from "react";
import { useAppDispatch } from "../../../app/hooks";
import Form from "../../../components/Form/Form";
import Button from "../../../components/Inputs/Button";
import EmailBox from "../../../components/Inputs/EmailBox";
import Textbox from "../../../components/Inputs/Textbox";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { EmployeeDTO } from "../../../models/DTOs/employees/EmployeeDTO";
import { PayrollDTO } from "../../../models/DTOs/payrolls/PayrollDTO";
import Translator from "../../../services/language/Translator";
import { onPayrollSaved } from "../payrollsActions";

const CreatePayroll = () => {
  const dispatch = useAppDispatch();
  const [employee, setEmployee] = useState({} as EmployeeDTO);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmployee({ ...employee, [e.currentTarget.name]: e.currentTarget.value });
  };

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmployee({ ...employee, email: e.currentTarget.value.trim() });
  };

  const handleCreatePayroll = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    dispatch(
      onPayrollSaved({
        employee: employee,
        companyId: "*********",
      } as PayrollDTO)
    );
  };

  return (
    <MainWindowContainer data-testid="create-payroll-container">
      <Form data-testid="create-payroll-form">
        <Form.Header data-testid="create-payroll-header">
          <Translator getString="Create payroll header" />
        </Form.Header>
        <Textbox
          data-testid="first-name-input"
          handleChange={handleChange}
          label="First name"
          placeholder="Enter first name"
          name="firstName"
          value={employee.firstName}
        />
        <Textbox
          data-testid="second-name-input"
          handleChange={handleChange}
          label="Second name"
          placeholder="Enter second name"
          name="secondName"
          value={employee.secondName}
        />
        <Textbox
          data-testid="last-name-input"
          handleChange={handleChange}
          label="Last name"
          placeholder="Enter last name"
          name="lastName"
          value={employee.lastName}
        />
        <Textbox
          data-testid="egn-input"
          handleChange={handleChange}
          label="EGN"
          placeholder="Enter EGN"
          name="egn"
          value={employee.egn}
        />
        <EmailBox
          data-testid="email-input"
          handleChange={handleEmailChange}
          label="E-mail"
          placeholder="Enter E-mail"
          name="email"
          value={employee.email}
        />
        <Button
          data-testid="create-payroll-button"
          label="strCreatePayroll"
          onClick={handleCreatePayroll}
        />
      </Form>
    </MainWindowContainer>
  );
};

export default CreatePayroll;
