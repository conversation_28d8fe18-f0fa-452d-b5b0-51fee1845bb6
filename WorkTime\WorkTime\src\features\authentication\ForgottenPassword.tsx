import { useParams } from "react-router-dom";
import Container from "../../components/Container";
import Textbox from "../../components/Inputs/Textbox";
import { ChangeEvent, MouseEvent, useState } from "react";
import { isValidEmail } from "../../services/emailService";
import Button from "../../components/Inputs/Button";
import Label from "../../components/Inputs/Label";
import styled from "styled-components";
import { post } from "../../services/connectionService";
import { ResetPasswordRequest } from "../../models/Requests/ResetPasswordRequest";
import Alert from "../../components/Inputs/Alert";
import MainWindowContainer from "../../components/MainWindowContainer";

const ResetPasswordContainer = styled(MainWindowContainer)`
  width: clamp(30%, 20rem, 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
`;

const DataContainer = styled(Container)`
  width: 100%;
`;

const ResetPasswordButton = styled(Button)`
  width: 100%;
`;

const ForgottenPassword = () => {
  const { email } = useParams();
  const [currentEmail, setCurrentEmail] = useState(email ?? "");
  const [isEmailSent, setIsEmailSent] = useState(false);

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCurrentEmail(e.currentTarget.value);
  };

  const handleResetPasswordClick = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    post("sso/reset-password", {
      resetPasswordDTO: {
        email: email,
        callback: `${window.location.origin}/auth/change-forgotten-password/${currentEmail}/`,
      },
    } as ResetPasswordRequest).then(() => {
      setIsEmailSent(true);
    });
  };

  return (
    <ResetPasswordContainer>
      <DataContainer>
        <Label data-testid="reset-password-label">Reset password</Label>
        <Textbox
          value={currentEmail}
          handleChange={handleEmailChange}
          label="E-mail"
          data-testid="email-textbox"
        />
        <ResetPasswordButton
          label="Reset password"
          onClick={handleResetPasswordClick}
          disabled={!isValidEmail(currentEmail)}
          data-testid="reset-password-button"
        />
        {isEmailSent && (
          <Alert
            type="success"
            message="Email sent successfully!"
            data-testid="success-alert"
          />
        )}
      </DataContainer>
    </ResetPasswordContainer>
  );
};

export default ForgottenPassword;
