import { useState } from "react";
import styled from "styled-components";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import NewAddresses from "./NewAddresses";
import NewIdentificationCardData from "./NewIdentificationCardData";
import NewPayroll from "./NewPayroll";
import NewPersonalData from "./NewPersonalData";

// Add employee data state interface
interface EmployeeFormData {
  personalData: {
    isValid: boolean;
    data: any;
  };
  identificationData: {
    isValid: boolean;
    data: any;
  };
  addressData: {
    isValid: boolean;
    data: any;
  };
  payrollData: {
    isValid: boolean;
    data: any;
  };
}

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ isActive: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem;
  background-color: ${(p) =>
    p.isActive
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.isActive
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.isActive ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-bottom: 3rem;
`;

const NavigationContainer = styled(Container)`
  display: flex;
  justify-content: flex-end;
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 1rem;
  width: 100%;
`;

const NextButton = styled(Button)`
  width: 8rem;
`;

const NewEmployeeSideMenu = () => {
  const [activeStep, setActiveStep] = useState(1);
  const [formData, setFormData] = useState<EmployeeFormData>({
    personalData: { isValid: false, data: {} },
    identificationData: { isValid: false, data: {} },
    addressData: { isValid: false, data: {} },
    payrollData: { isValid: false, data: {} },
  });

  // Helper function to check if current step is valid
  const isCurrentStepValid = () => {
    switch (activeStep) {
      case 1:
        return formData.personalData.isValid;
      case 2:
        return formData.identificationData.isValid;
      case 3:
        return formData.addressData.isValid;
      case 4:
        return formData.payrollData.isValid;
      default:
        return false;
    }
  };

  // Handlers for each form's validation and data
  const handlePersonalDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      personalData: { isValid, data },
    });
  };

  const handleIdentificationDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      identificationData: { isValid, data },
    });
  };

  const handleAddressDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      addressData: { isValid, data },
    });
  };

  const handlePayrollDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      payrollData: { isValid, data },
    });
  };

  const steps = [
    {
      id: 1,
      label: "1. Лични данни",
      component: (
        <NewPersonalData
          onValidation={handlePersonalDataValidation}
          data={formData.personalData.data}
        />
      ),
    },
    {
      id: 2,
      label: "2. Данни по ЛК",
      component: (
        <NewIdentificationCardData
          onValidation={handleIdentificationDataValidation}
          data={formData.identificationData.data}
        />
      ),
    },
    {
      id: 3,
      label: "3. Адреси",
      component: (
        <NewAddresses
          onValidation={handleAddressDataValidation}
          data={formData.addressData.data}
        />
      ),
    },
    {
      id: 4,
      label: "4. Назначение",
      component: (
        <NewPayroll
          onValidation={handlePayrollDataValidation}
          data={formData.payrollData.data}
        />
      ),
    },
  ];

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId);
  };

  const handleNextClick = () => {
    if (activeStep < steps.length && isCurrentStepValid()) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleSubmit = () => {
    if (isCurrentStepValid()) {
      // Combine all data and submit
      const completeEmployeeData = {
        ...formData.personalData.data,
        ...formData.identificationData.data,
        ...formData.addressData.data,
        ...formData.payrollData.data,
      };

      // Here you would call your API to save the employee data
      console.log("Submitting employee data:", completeEmployeeData);

      // You could navigate away or show success message
    }
  };

  return (
    <MainContainer data-testid="new-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            isActive={activeStep === step.id}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        {steps.find((step) => step.id === activeStep)?.component}
      </ContentContainer>

      <NavigationContainer data-testid="navigation-container">
        {activeStep === steps.length ? (
          <NextButton
            label="Finish"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid()}
            data-testid="submit-button"
          />
        ) : (
          <NextButton
            label="Next"
            onClick={handleNextClick}
            disabled={!isCurrentStepValid()}
            data-testid="next-button"
          />
        )}
      </NavigationContainer>
    </MainContainer>
  );
};

export default NewEmployeeSideMenu;
