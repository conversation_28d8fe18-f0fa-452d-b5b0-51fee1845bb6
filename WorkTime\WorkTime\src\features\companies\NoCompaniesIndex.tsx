import { useContext, useEffect, useState } from "react";
import Button from "../../components/Inputs/Button";
import JoinCompany from "./JoinCompany";
import styled, { css } from "styled-components";
import plusImage from "../../assets/images/button/plus.png";
import plusHoverImage from "../../assets/images/button/plusHover.png";
import plusDisabledImage from "../../assets/images/button/plusDisable.png";
import moveRightImage from "../../assets/images/button/moveRight.png";
import moveRightHoverImage from "../../assets/images/button/moveRightHover.png";
import moveRightDisabledImage from "../../assets/images/button/moveRightDisabled.png";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import Container from "../../components/Container";
import { HeaderType } from "../../models/Enums/HeaderType";
import { Header } from "../../components/Header";
import InitialCreateCompany from "./InitialCreateCompany";
import { AuthContext } from "../authentication/AuthContext";

const NoCompaniesContainer = styled(Container)`
  display: grid;
  place-items: center;
  height: 100vh;
`;

const NoCompaniesWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  place-items: center;
  width: clamp(20%, 34rem, 100%);
  margin: 0 auto;
  height: 45rem;
  overflow: auto;
`;

const WelcomeHeader = styled(Header)`
  width: 20rem;
  text-align: center;
  font-weight: 400;
  font-size: 1.6rem;
  margin-bottom: 0;
`;

const StyledHeader = styled(Header)`
  width: 32rem;
  text-align: center;
  font-weight: 400;
  font-size: 1.6rem;
  margin: 0 auto;
`;

const ButtonsContainer = styled(Container)`
  margin-bottom: 0.625rem;
  margin-top: 3rem;
  width: clamp(20%, 32rem, 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
`;

const ContentContainer = styled(Container)`
  width: clamp(20%, 32rem, 100%);
  top: 13rem;
  overflow: auto;

  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  bottom: 3rem;
  left: 0;
  right: 0;
`;

const CompanyButton = styled(Button)<{
  companyImage: string;
  companyHoverImage: string;
  companyDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--company-button-background-color);
  color: var(--company-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.companyImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 0.7rem 0 0.7rem 3.5rem;
  text-align: left;
  cursor: default;

  ${(p) => p.isDisable && Disable}

  ${(p) =>
    p.isDisable &&
    css`
      &:hover {
        background-image: url(${p.companyHoverImage});
        background-color: var(--company-button-background-color-hover);
        color: var(--company-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const Disable = css<{
  companyDisabledImage: string;
}>`
  background-image: url(${(p) => p.companyDisabledImage});
  background-color: var(--company-button-background-color-disable);
  color: var(--company-button-color-disable);
`;

enum TabItems {
  Import = "import",
  Create = "create",
  Join = "join",
  MyCompanies = "myCompanies",
}

const CompaniesSideMenuIndex = () => {
  const [company, setCompany] = useState({} as CompanyDTO);
  const { user } = useContext(AuthContext);
  const isEmployee = user.workTimeRoleName === "grEmployee";
  const [activeTab, setActiveTab] = useState(isEmployee ? "join" : "create");

  useEffect(() => {
    if (Object.keys(company || {}).length === 0) {
      setCompany({
        name: "Фирми за импорт",
        bulstat: "",
        contactName: "",
        userRegistrationCompanyId: 0,
        id: "",
      });
    }
  }, [company]);

  const handleSetTab = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <NoCompaniesContainer data-testid="no-companies-container">
      <NoCompaniesWrapper data-testid="no-companies-wrapper">
        <WelcomeHeader
          data-testid="welcome-header"
          content="Welcome!"
          headerType={HeaderType.H2}
        />
        <StyledHeader
          data-testid="styled-header"
          content="strWelcomeToWorkTimeFirst"
          headerType={HeaderType.H2}
        />
        <ButtonsContainer data-testid="buttons-container">
          {!isEmployee && (
            <CompanyButton
              data-testid="create-company-button"
              label="Create"
              companyImage={plusImage}
              companyHoverImage={plusHoverImage}
              companyDisabledImage={plusDisabledImage}
              onClick={() => handleSetTab("create")}
              isDisable={activeTab !== "create"}
            />
          )}

          <CompanyButton
            data-testid="join-company-button"
            label="Join company"
            companyImage={moveRightImage}
            companyHoverImage={moveRightHoverImage}
            companyDisabledImage={moveRightDisabledImage}
            onClick={() => handleSetTab("join")}
            isDisable={activeTab !== "join"}
          />
        </ButtonsContainer>
        <ContentContainer data-testid="content-container">
          {activeTab === TabItems.Create && (
            <InitialCreateCompany data-testid="initial-create-company" />
          )}
          {activeTab === TabItems.Join && (
            <JoinCompany data-testid="join-company" />
          )}
        </ContentContainer>
      </NoCompaniesWrapper>
    </NoCompaniesContainer>
  );
};

export default CompaniesSideMenuIndex;
