import React from "react";
import styled from "styled-components";
import ArrowIcon from "../../assets/images/arrows/arrow.png";
import Image from "../Image";

interface ArrowButtonProps {
  direction: "left" | "right";
  onClick: () => void;
}

const StyledArrow = styled(Image)<{ direction: "left" | "right" }>`
  cursor: pointer;
  transform: ${({ direction }) =>
    direction === "left" ? "rotate(270deg)" : "rotate(90deg)"};
  width: 3rem;
  height: 1rem;
  position: absolute;
  top: 2rem;
  ${({ direction }) => direction}: 1rem;
`;

export const ArrowButton: React.FC<ArrowButtonProps> = ({
  direction,
  onClick,
}) => {
  return (
    <StyledArrow
      src={ArrowIcon}
      direction={direction}
      onClick={onClick}
      alt="Arrow"
      data-testid={`arrow-button-${direction}`}
    />
  );
};
