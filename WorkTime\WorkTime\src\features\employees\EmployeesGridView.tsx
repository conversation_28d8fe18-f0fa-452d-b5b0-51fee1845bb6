import { ChangeEvent, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import MainWindowContainer from "../../components/MainWindowContainer";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import { onEmployeesLoaded, selectEmployees } from "./employeesActions";
import Searchbar from "../../components/Inputs/Searchbar";
import styled from "styled-components";
import Container from "../../components/Container";
import { useNavigate } from "react-router-dom";
import EmployeesGridElements from "./EmployeesGridElements";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem;
  height: 45rem;
  overflow-y: auto;
`;

const EmployeesContainer = styled.div`
  display: flex;
  justify-content: center;
  width: clamp(30%, 90rem, 90%);
  margin: 2rem;
`;

const SearchbarContainer = styled(Container)`
  position: relative;
  box-sizing: border-box;
  justify-content: center;
  width: clamp(35%, 55rem, 70%);
  margin: 0 auto;
`;

const EmployeesGridView = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const employees = useAppSelector(selectEmployees).employees;
  const [filteredEmployees, setFilteredEmployees] = useState(employees);
  const [textInput, setTextInput] = useState("");

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
    companyId && dispatch(onEmployeesLoaded(companyId));
  }, [dispatch]);

  useEffect(() => {
    setFilteredEmployees(employees);
  }, [employees]);

  const onRowClick = (id: string) => {
    navigate(`${id}`);
  };

  const employeesFilter = (searchText: string) => {
    setFilteredEmployees(
      employees.filter((employee) =>
        employee?.firstName
          .toLocaleLowerCase()
          .includes(searchText?.toLocaleLowerCase())
      )
    );
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    setTextInput(searchText);
    employeesFilter(searchText);
  };

  return (
    <MainContainer data-testid="employees-main-container">
      <SearchbarContainer data-testid="employees-searchbar-container">
        <Searchbar
          data-testid="employees-searchbar"
          handleChange={handleChange}
          value={textInput}
          type="text"
          label="strGetInput"
          placeholder={""}
        ></Searchbar>
      </SearchbarContainer>
      <EmployeesContainer data-testid="employees-grid-container">
        <EmployeesGridElements
          data-testid="employees-grid-elements"
          data={filteredEmployees.map((curData) => ({
            id: curData.workTimeId,
            name: [curData.firstName, curData.secondName, curData.lastName]
              .filter((name) => name != null)
              .join(" "),
            email: curData.email,
          }))}
          handleClick={onRowClick}
          imgSize={3}
        />
      </EmployeesContainer>
    </MainContainer>
  );
};

export default EmployeesGridView;
