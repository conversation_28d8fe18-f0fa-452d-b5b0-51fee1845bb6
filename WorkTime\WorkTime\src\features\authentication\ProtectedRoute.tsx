import { useContext, useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isAuthenticated } from "../../services/authentication/authenticationService";
import { AuthContext } from "./AuthContext";

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

const PrivateRoute = ({ children }: Props) => {
  const { user } = useContext(AuthContext);
  const [authenticated, setAuthenticated] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const path = useLocation();

  useEffect(() => {
    const authed = isAuthenticated();
    setAuthenticated(authed);
    setIsLoaded(true);
  }, [setAuthenticated]);

  return (
    <>
      {/* {isLoaded ? (
        authenticated ? (
          user.hasSignedIn ? (
            children
          ) : path.pathname.includes(`/auth/change-password`) ? (
            children
          ) : (
            <Navigate
              to={`/auth/change-password?returnAfterPasswordChanged=${path.pathname}`}
            />
          )
        ) : (
          <Navigate to={`/auth/login?returnAfterLogin=${path.pathname}`} />
        )
      ) : (
        // Тука може да се сложи някакъв Spinner / Loader etc
        <></>
      )}
       */}
      {children}
    </>
  );
};

export default PrivateRoute;
