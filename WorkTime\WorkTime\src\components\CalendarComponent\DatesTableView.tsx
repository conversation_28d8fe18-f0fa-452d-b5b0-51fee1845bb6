import React, { useState } from "react";
import styled from "styled-components";
import Container from "../Container";
import { CalendarHeader } from "./CalendarHeader";
import { DayOfWeekHeader } from "./DayOfWeekHeader";
import { DayCell } from "./DayCell";
import { EmployeeInfo } from "./types/types";

const CalendarContainer = styled(Container)`
  display: block;
  position: relative;
  align-items: center;
  justify-content: center;
  border-radius: 0em 0em 1.8em 1.8em;
  width: 100%;
  padding: 0em 0.6em 0.6em 0.6em;
  background-color: var(--datepicker-view-border);
`;

const ContainerDays = styled(Container)`
  position: relative;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  width: 100%;
  overflow: hidden;
`;

export interface DayInfo {
  dayNumber: number;
  type: string;
  missingEmployees: EmployeeInfo[];
}

interface DatesTableViewProps {
  selectedMonth: number;
  selectedYear: number;
  days: DayInfo[];
  employeeRowPositions: { [week: number]: { [employeeId: string]: number } };
  onPrevMonth: () => void;
  onNextMonth: () => void;
  isCurrentDay: (day: number) => boolean;
  selectedEmployeeId?: string;
  hoveredEmployeeId?: string;
}

const DatesTableView: React.FC<DatesTableViewProps> = ({
  selectedMonth,
  selectedYear,
  days,
  employeeRowPositions,
  onPrevMonth,
  onNextMonth,
  isCurrentDay,
  selectedEmployeeId,
  hoveredEmployeeId,
}) => {
  const [selectionStart, setSelectionStart] = useState<number | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<number | null>(null);

  const isSelected = (dayNumber: number) => {
    if (selectionStart !== null && selectionEnd !== null) {
      debugger;
      return dayNumber >= selectionStart && dayNumber <= selectionEnd;
    }
    return false;
  };

  return (
    <>
      <CalendarHeader
        data-testid="calendar-header"
        selectedMonth={selectedMonth}
        selectedYear={selectedYear}
        goToPrevMonth={onPrevMonth}
        goToNextMonth={onNextMonth}
      />
      <CalendarContainer data-testid="calendar-container">
        <DayOfWeekHeader data-testid="day-of-week-header" />
        <ContainerDays data-testid="container-days">
          {days.map(({ dayNumber, type, missingEmployees }, index) => (
            <DayCell
              data-testid={`day-cell-${index}`}
              key={`day-${index}`}
              dayNumber={dayNumber}
              isCurrentMonth={type === "currentMonth"}
              isCurrentDay={isCurrentDay(dayNumber)}
              missingEmployees={missingEmployees}
              numberOfRows={
                Object.keys(employeeRowPositions[Math.floor(index / 7)] ?? {})
                  .length
              }
              selectedEmployeeId={selectedEmployeeId}
              hoveredEmployeeId={hoveredEmployeeId}
            />
          ))}
        </ContainerDays>
      </CalendarContainer>
    </>
  );
};

export default DatesTableView;
