import React from "react";
import styled from "styled-components";
import { IGridViewEntity } from "../models/Interfaces/IGridViewEntity";

interface GridViewProps<T extends IGridViewEntity> {
  data: T[];
  renderElement: (item: T) => React.ReactNode;
}

const GridViewContainer = styled.div<{ columnCount: number }>`
  margin-top: 30rem;
  display: grid;
  width: ${({ columnCount }) => `${columnCount * 14}rem`};
  grid-template-columns: repeat(${(props) => props.columnCount}, 1fr);
  grid-gap: 1.8rem;
  justify-items: center;
  margin: 0 auto;
`;

export const GridViewDiv = styled.div`
  height: 12rem;
  width: 12rem;
  cursor: pointer;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  transition: transform 0.3s ease-in-out, border 0.3s ease-in-out;
  box-shadow: 0px 2px 5px var(--container-box-shadow-gridView);
  background: var(--container-backround-gridView);
  cursor: pointer;
  transform-origin: center;

  &:hover {
    border: 0.2rem solid var(--container-hover-border-gridView);
    transform: scale(1.04);
  }
`;

const GridView = <T extends IGridViewEntity>({
  data,
  renderElement,
}: GridViewProps<T>) => {
  // Determine the number of columns to ensure rows are as balanced as possible
  const calculateColumnCount = (items: number) => {
    if (items <= 5) return items;
    let cols = Math.ceil(items / Math.ceil(items / 5)); // Dynamic column calculation
    return cols;
  };

  const columnCount = calculateColumnCount(data.length);

  return (
    <GridViewContainer
      columnCount={columnCount}
      data-testid="grid-view-container"
    >
      {data.map((item) => renderElement(item))}
    </GridViewContainer>
  );
};

export default GridView;
