# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- release

pool:
  vmImage: 'windows-latest'
  name: 'Jurassic'
variables:
  - group: prod-variables
  - name: VITE_GATEWAY_API
    value: Production

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
    displayName: 'Install Node.js'
  - script: |
      echo "Setting environment variables..."
      setx VITE_GATEWAY_API "$(VITE_GATEWAY_API)"
      
      echo "Installing dependencies"
      npm install
    displayName: 'Setup and Install'
  - script: |
      echo "Building..."
      npm run build
    displayName: "Building"
  - task: WindowsMachineFileCopy@2
    displayName: 'Copy Files to Windows Machine'
    inputs:
      SourcePath: '$(Build.SourcesDirectory)/dist' 
      MachineNames: '$(MachineName)' 
      AdminUserName: '$(Username)'
      AdminPassword: '$(Password)'
      TargetPath: 'C:\inetpub\wwwroot\worktime'