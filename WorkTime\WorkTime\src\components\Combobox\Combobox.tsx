import React from "react";
import styled from "styled-components";
import Image from "../Image";
import Arrow from "../../assets/images/arrows/down-filled-triangular-arrow.png";
import Label from "../Inputs/Label";
import { useEffect, useRef, useState } from "react";
import Container from "../Container";

const OutsideContainer = styled.div<{ isHidden: boolean }>`
  position: relative;
  min-width: 12rem;
  opacity: ${(props) => (props.isHidden ? "0" : "1")};
  transition: 0.3s ease-in-out opacity;
  margin-left: 0.2em;
`;

const Rows = styled(Container)<{ isOpen: boolean }>`
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 2.7rem;
  margin-top: 0.4em;
  background-color: var(--combobox-background-color);

  border-radius: ${({ isOpen }) => (isOpen ? "2rem 2rem 0rem 0rem" : "2rem")};
  transition: border-radius 0.5s;
`;

const StyledLabel = styled(Label)`
  padding-left: 1rem;
`;

const ArrowImage = styled(Image)`
  margin-left: -1rem;
  position: absolute;
  top: 40%;
  right: 5%;
  z-index: 2;
  width: 0.8rem;
  height: 0.8rem;
`;

const Rectangle = styled.div<{
  chosen: boolean;
  isOpen: boolean;
  index: number;
  isLast: boolean;
}>`
  box-sizing: border-box;
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--combobox-background-color);
  z-index: ${({ chosen }) => (chosen ? 2 : 1)};
  top: ${({ isOpen, chosen, index }) =>
    isOpen && !chosen ? `calc( 2.7rem * ${index})` : "0"};

  border-radius: ${({ isOpen, chosen, isLast }) =>
    isOpen && !chosen ? (isLast ? "0rem 0rem 2rem 2rem" : "0rem") : "2rem"};
  transition: top 0.5s, border-radius 0.4s;

  justify-content: left;
  align-items: center;
  cursor: pointer;
`;

interface ComboboxProps {
  options: string[];
  onChange: (chosenOption: string) => void;
  isHidden?: boolean;
}

const Combobox: React.FC<ComboboxProps> = ({ options, onChange, isHidden }) => {
  const [currentOptions, setCurrentOptions] = useState<string[]>(options);
  const [chosenOption, setChosenOption] = useState(options[0]);
  const [isOpen, setIsOpen] = useState(false);
  const comboboxRef = useRef<HTMLDivElement | null>(null);

  const handleRectClick = (option: string) => {
    if (option === chosenOption) {
      setIsOpen(!isOpen);
    } else {
      setChosenOption(option);
      setIsOpen(false);
      onChange(option);

      const newOrderOptions = [
        option,
        ...currentOptions.filter((opt) => opt !== option),
      ];

      setTimeout(() => {
        setCurrentOptions(newOrderOptions);
      }, 300);
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      comboboxRef.current &&
      !comboboxRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <OutsideContainer
      data-testid="combobox-container"
      isHidden={isHidden ?? false}
      ref={comboboxRef}
    >
      <Rows data-testid="combobox-rows" isOpen={isOpen}>
        {currentOptions.map((option, index) => (
          <Rectangle
            data-testid={`combobox-option-${index}`}
            index={index}
            key={option}
            chosen={chosenOption === option}
            isOpen={isOpen}
            isLast={index === options.length - 1}
            onClick={(e) => {
              e.stopPropagation();
              handleRectClick(option);
            }}
          >
            <StyledLabel data-testid={`combobox-label-${index}`}>
              {option}
            </StyledLabel>
          </Rectangle>
        ))}
        <ArrowImage data-testid="combobox-arrow" src={Arrow} alt="Arrow" />
      </Rows>
    </OutsideContainer>
  );
};

export default Combobox;
