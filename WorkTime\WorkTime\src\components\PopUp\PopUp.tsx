import React, { useEffect, useState } from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface PopUpProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  title: string;
  children: React.ReactNode | React.ReactNode[];
}

const PopUpWrapper = styled.div`
  padding: 0.3rem 0 0.5rem 0;
`;

const PopUpDiv = styled.div<{ isVisible: boolean }>`
  visibility: ${(p) => (p.isVisible ? "visible" : "hidden")};
  opacity: ${(p) => (p.isVisible ? "1" : "0")};
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
`;

const TermsDiv = styled.div`
  margin: 4.375rem auto;
  padding: 1.25rem;
  background: #fff;
  border-radius: 1.5rem;
  width: 30%;
  position: relative;
  transition: all 5s ease-in-out;
`;

const CloseSpan = styled.span`
  position: absolute;
  top: 1.25rem;
  right: 1.875rem;
  transition: all 200ms;
  font-size: 1.875rem;
  font-weight: bold;
  text-decoration: none;
  color: var(--pop-up-close-cloor);
  cursor: pointer;
`;

const TermsSpan = styled.span`
  cursor: pointer;
  padding: 0.5rem 0 0.5rem 0;
`;

const PopUp = (props: PopUpProps) => {
  const { children, title } = props;
  const [show, setShow] = useState(false);

  useEffect(() => {
    setShow(show);
  }, [show]);

  const closeHandler = (e: React.MouseEvent<HTMLButtonElement>) => {
    setShow(false);
  };

  const handleBtnClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setShow((prev) => !prev);
  };

  return (
    <PopUpWrapper data-testid="popup-wrapper">
      <TermsSpan onClick={handleBtnClick} data-testid="terms-button">
        <Translator getString="Accept Terms" />
      </TermsSpan>
      <PopUpDiv isVisible={show} data-testid="popup-overlay">
        <TermsDiv data-testid="terms-content">
          <h2 data-testid="popup-title">{title}</h2>
          <CloseSpan onClick={closeHandler} data-testid="close-button">
            &times;
          </CloseSpan>
          <div data-testid="popup-content">{children}</div>
        </TermsDiv>
      </PopUpDiv>
    </PopUpWrapper>
  );
};

export default PopUp;
