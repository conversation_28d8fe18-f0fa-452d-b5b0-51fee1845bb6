import { useAppDispatch } from "../../../app/hooks";
import { useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { onGetCompanies } from "../companiesActions";
import { CompanyContext } from "../CompanyContext";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import {
  LOCAL_STORAGE_COMPANY_ID,
  LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID,
} from "../../../constants/local-storage-constants";
import MainWindowContainer from "../../../components/MainWindowContainer";
import CompaniesGridElements from "./CompaniesGridElements";

interface GridViewProps {
  data: CompanyDTO[];
}
const CompaniesGridView: React.FC<GridViewProps> = ({ data }) => {
  const dispatch = useAppDispatch();
  const { setCompany } = useContext(CompanyContext);
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(onGetCompanies());
  }, [dispatch]);

  const selectCompany = (id: string) => {
    const company = data.find((companyItem) => companyItem.id === id);

    if (company) {
      setCompany(company);

      localStorage.setItem(LOCAL_STORAGE_COMPANY_ID, company.id);
      localStorage.setItem(
        LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID,
        company.userRegistrationCompanyId.toString()
      );
      navigate(`/${company.id}/employees`);
    } else {
      console.error(`Failed to find company with id: ${id}`);
    }
  };

  return (
    <MainWindowContainer data-testid="main-window-container">
      <CompaniesGridElements
        data-testid="companies-grid-elements"
        data={data.map((curData) => ({
          id: curData.id,
          name: curData.name,
          bulstat: curData.bulstat,
        }))}
        handleClick={selectCompany}
        imgSize={5.5}
      />
    </MainWindowContainer>
  );
};

export default CompaniesGridView;
