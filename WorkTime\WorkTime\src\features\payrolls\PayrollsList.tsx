import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import Table, { ColumnDefinitionType } from "../../components/Table/Table";
import {
  onPayrollsLoaded,
  selectPayrolls,
  onPayrollDelete,
} from "../payroll/payrollsActions";
import Avatar from "../../components/Table/Avatar";
import MainWindowContainer from "../../components/MainWindowContainer";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import { IEntity } from "../../models/DTOs/IEntity";

interface PayrollEmployeeDTO extends IEntity {
  avatar: React.ReactNode;
  firstName: string;
  id: string;
  egn: string;
  email: string;
  position: string;
  department: string;
  phone: string;
  deleteP: string;
  contractId: string;
  contractNumber: string;
}

const ShowPayrolls = () => {
  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls).payrolls;
  const [filteredPayrolls, setFilteredPayrolls] = useState(payrolls);

  const columns: ColumnDefinitionType<
    PayrollEmployeeDTO,
    keyof PayrollEmployeeDTO
  >[] = [
    {
      key: "avatar",
      value: "",
    },
    {
      key: "firstName",
      value: "Име",
    },
    {
      key: "email",
      value: "Email",
    },
    {
      key: "contractId",
      value: "Тип назначение",
    },
    {
      key: "contractNumber",
      value: "№",
    },
  ];

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
    dispatch(onPayrollsLoaded(companyId));
  }, [dispatch]);

  useEffect(() => {
    setFilteredPayrolls(payrolls);
  }, [payrolls]);

  const handleDeletePayroll = async (id: string) => {
    dispatch(onPayrollDelete(id));
  };

  const filterColumns = (columnKey: string, searchText: string) => {
    setFilteredPayrolls(
      payrolls.filter((payroll) =>
        ((payroll.employee as any)[columnKey] as string)
          .toLocaleLowerCase()
          .includes(searchText.toLocaleLowerCase())
      )
    );
  };

  const filtertedData = filteredPayrolls?.map((p) => {
    return {
      id: p.workTimeId,
      workTimeId: p.workTimeId,
      avatar: (
        <Avatar
          name={p.employee.firstName}
          photo={""}
          size={2}
          data-testid={`payroll-avatar-${p.workTimeId}`}
        />
      ),
      firstName: p.employee.firstName,
      egn: p.employee.egn,
      email: p.employee.email,
      contractId: p.contract.name,
      contractNumber: p.contractNumber,
    } as PayrollEmployeeDTO;
  });

  return (
    <MainWindowContainer data-testid="payrolls-container">
      <Table
        data={filtertedData}
        filterColumns={filterColumns}
        columns={columns}
        data-testid="payrolls-table"
      ></Table>
    </MainWindowContainer>
  );
};

export default ShowPayrolls;
