import {
  LOCAL_STORAGE_ACCESS_TOKEN,
  LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID,
  LOCAL_STORAGE_REFRESH_TOKEN,
} from "../constants/local-storage-constants";
import { GATEWAY_API_PATH } from "../constants/api-constants";
import { ValidationResult } from "../models/Results/ValidationResult";
import { toast } from "react-toastify";
import { translate } from "./language/Translator";

export async function get<T>(url: string, headers?: any): Promise<T> {
  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "GET",
    headers: headers,
  });
  if (response.ok) {
    try {
      return (await response.json()) as T;
    } catch (ex) {
      throw ex;
    }
  }

  return [] as unknown as T;
}

export async function authenticatedGet<T>(
  url: string,
  headers: any = []
): Promise<T> {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN) ?? "";
  const refreshToken = localStorage.getItem(LOCAL_STORAGE_REFRESH_TOKEN) ?? "";
  const userRegistrationCompanyId =
    localStorage.getItem(LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID) ?? "";

  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "GET",
    headers: {
      Authorization: accessToken,
      "Refresh-Token": refreshToken,
      "company-userregistration-id": userRegistrationCompanyId,
      ...headers,
    },
  });

  if (response.ok) {
    localStorage.setItem(
      LOCAL_STORAGE_ACCESS_TOKEN,
      response.headers.get("Authorization") ?? accessToken
    );
    localStorage.setItem(
      LOCAL_STORAGE_REFRESH_TOKEN,
      response.headers.get("Refresh-token") ?? refreshToken
    );
    try {
      return (await response.json()) as T;
    } catch (ex) {
      return undefined as T;
    }
  }

  return [] as unknown as T;
}

export async function getResponse(url: string, headers?: any): Promise<any> {
  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "GET",
    headers: headers,
  });

  return response;
}

export async function post<T>(
  url: string,
  data: any,
  headers?: any
): Promise<T> {
  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "POST",
    credentials: "omit",
    cache: "no-cache",
    body: JSON.stringify(data),
    headers: {
      "content-type": "application/json",
      ...headers,
    },
  });

  if (response.ok) {
    try {
      return (await response.json()) as T;
    } catch (ex) {
      return {} as T;
    }
  }

  throw response.status;
}

export async function authenticatedPost<T>(
  url: string,
  data: any = {},
  headers?: any
): Promise<T> {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN) ?? "";
  const refreshToken = localStorage.getItem(LOCAL_STORAGE_REFRESH_TOKEN) ?? "";
  const userRegistrationCompanyId =
    localStorage.getItem(LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID) ?? "";

  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "POST",
    credentials: "omit",
    cache: "no-cache",
    body: JSON.stringify(data),
    headers: {
      "content-type": "application/json",
      Authorization: accessToken,
      "Refresh-Token": refreshToken,
      "company-userregistration-id": userRegistrationCompanyId,
      ...headers,
    },
  });

  if (response.ok) {
    try {
      return (await response.json()) as T;
    } catch (ex) {
      return undefined as T;
    }
  } else {
    try {
      const validationResult = (await response.json()) as
        | ValidationResult
        | undefined;
      if (validationResult && validationResult.hasValidationErrors) {
        validationResult.validationErrors.forEach((validationError) => {
          toast.error(translate(validationError));
        });
      }
    } catch (error) {
      throw error;
    }
  }

  throw response.status;
}

export async function authenticatedDelete<T>(
  url: string,
  data?: any,
  headers?: any
): Promise<T> {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN) ?? "";
  const refreshToken = localStorage.getItem(LOCAL_STORAGE_REFRESH_TOKEN) ?? "";
  const userRegistrationCompanyId =
    localStorage.getItem(LOCAL_STORAGE_COMPANY_USER_REGISTRATION_ID) ?? "";

  const response = await fetch(`${GATEWAY_API_PATH}${url}${data}`, {
    method: "DELETE",
    headers: {
      "content-type": "application/json",
      Authorization: accessToken,
      "Refresh-Token": refreshToken,
      "company-userregistration-id": userRegistrationCompanyId,
      ...headers,
    },
  });

  if (response.ok) {
    try {
      return (await response.json()) as T;
    } catch (ex) {
      return undefined as T;
    }
  }
  throw response.status;
}

export async function authenticatedPut<T>(
  url: string,
  data: any = {},
  headers?: any
): Promise<T> {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN) ?? "";
  const refreshToken = localStorage.getItem(LOCAL_STORAGE_REFRESH_TOKEN) ?? "";

  const response = await fetch(`${GATEWAY_API_PATH}${url}`, {
    method: "PUT",
    credentials: "omit",
    cache: "no-cache",
    body: JSON.stringify(data),
    headers: {
      "content-type": "application/json",
      Authorization: accessToken,
      "Refresh-Token": refreshToken,
      ...headers,
    },
  });

  if (response.ok) {
    try {
      if (response.headers.get("content-type")?.includes("application/json")) {
        return (await response.json()) as T;
      }
      return undefined as T;
    } catch (ex) {
      return undefined as T;
    }
  }

  throw response.status;
}
