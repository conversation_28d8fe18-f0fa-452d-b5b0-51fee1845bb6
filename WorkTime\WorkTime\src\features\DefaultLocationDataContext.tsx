import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import {
  initCities,
  initDistricts,
  initMunicipalities,
} from "../services/nomenclatures/defaultLocationDataService";
import { IEnumType } from "../models/DTOs/enums/IEnumType";
import { ICitiesType } from "../models/DTOs/enums/ICitiesType";

interface IDefaultLocationDataContext {
  districts: IEnumType[];
  municipalities: IEnumType[];
  cities: ICitiesType[];
  // more enums
}

interface DefaultLocationDataProps {
  children: ReactNode;
}

const DefaultLocationDataContext = createContext<IDefaultLocationDataContext>({
  districts: [],
  municipalities: [],
  cities: [],
});

export const DefaultLocationDataProvider: React.FC<
  DefaultLocationDataProps
> = ({ children }) => {
  const [enums, setEnums] = useState<IDefaultLocationDataContext>({
    districts: [],
    municipalities: [],
    cities: [],
    //more enums
  });

  useEffect(() => {
    initDistricts().then((districts) => {
      setEnums((enums) => ({ ...enums, districts }));
    });
    initMunicipalities().then((municipalities) => {
      setEnums((enums) => ({ ...enums, municipalities }));
    });
    initCities().then((cities) => {
      setEnums((enums) => ({ ...enums, cities }));
    });
    //more enum fetch
  }, []);

  return (
    <DefaultLocationDataContext.Provider value={enums}>
      {children}
    </DefaultLocationDataContext.Provider>
  );
};

export const useDefaultPlaces = () => useContext(DefaultLocationDataContext);
