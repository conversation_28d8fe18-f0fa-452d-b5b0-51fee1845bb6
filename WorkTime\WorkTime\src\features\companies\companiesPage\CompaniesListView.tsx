import styled from "styled-components";
import { useContext } from "react";
import { CompanyContext } from "../CompanyContext";
import { useNavigate } from "react-router-dom";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import Label from "../../../components/Inputs/Label";
import Container from "../../../components/Container";
import companyImg from "../../../assets/images/companies/test-company-logo.png";
import { setCompanyLocalStorage } from "../../../services/companies/companiesService";
import { translate } from "../../../services/language/Translator";

const Image = styled.img`
  width: 2rem;
  height: 2rem;
  cursor: pointer;
`;

const CompanyName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
`;

const EIK = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  position: relative;
  cursor: pointer;
  text-align: center;
`;

const ContactNameCotnainer = styled(Container)`
  text-align: end;
`;

const ContactName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
`;

const CompanyContainer = styled(Container)`
  display: grid;
  padding-left: 1rem;
  padding-right: 1rem;
  border: none;
  grid-template-columns: 1.5rem 23rem 1fr 1.5fr;
  align-items: center;
  height: 3.5rem;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  margin: 0.1rem;
  cursor: pointer;
  overflow: hidden;
  gap: 1.5rem;
  transition: background-color 0.3s ease-in-out;
  &:hover {
    background-color: var(--listview-backround-hover-button);
  }
`;

const CompaniesContainer = styled(Container)`
  display: grid;
  align-items: center;
  width: clamp(35%, 55rem, 70%);
`;

interface ListViewProps {
  data: CompanyDTO[] | null;
}

const CompaniesListView: React.FC<ListViewProps> = ({ data }) => {
  const { setCompany } = useContext(CompanyContext);
  const navigate = useNavigate();

  const selectCompany = (company: CompanyDTO) => {
    setCompany(company);

    setCompanyLocalStorage(company);

    navigate(`/${company.id}/employees`);
  };
  return (
    <CompaniesContainer>
      {data && data.length > 0 ? (
        data.map((c) => (
          <CompanyContainer key={c.name} onClick={() => selectCompany(c)}>
            <Image src={companyImg} alt={c.name} />
            <CompanyName>{c.name}</CompanyName>
            <Container>
              <EIK>{translate("EIK:")}</EIK> <EIK>{c.bulstat}</EIK>
            </Container>
            <ContactNameCotnainer>
              <ContactName>{translate("MOL:")}</ContactName>{" "}
              <ContactName>{c.contactName}</ContactName>
            </ContactNameCotnainer>
          </CompanyContainer>
        ))
      ) : (
        <CompanyName>{translate("No companies found")}</CompanyName>
      )}
    </CompaniesContainer>
  );
};

export default CompaniesListView;
