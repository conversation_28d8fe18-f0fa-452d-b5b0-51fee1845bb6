import React from "react";
import styled from "styled-components";
import Container from "../Container";
import { AlignmentPosition, EmployeeInfo } from "./types/types";
import Avatar from "./Avatar";
import { ColorService } from "../../services/ColorService/ColorService";
import { generateColorFromName } from "../../utils/colorUtils";

const StyledAvatar = styled(Avatar)``;

const StyledDayBox = styled(Container)<{ numberOfRows: number }>`
  position: relative;
  display: grid;
  grid-template-rows: repeat(${(props) => props.numberOfRows}, 1fr);
  height: 90%;
  width: 102%;
  top: 0.6em;
  left: -0.3em;
  z-index: 99;
  background-color: transparent;
  cursor: pointer;
  overflow: hidden;
  user-select: none;
`;

const StyledLine = styled(Container)<{
  bgcolor: string;
  row: number;
  numberOfRows: number;
  alignmentPosition: AlignmentPosition;
  isSelected: boolean;
  isHovered: boolean;
}>`
  grid-row-start: ${(props) => props.row};
  width: 100%;
  height: ${(props) => {
    if (props.numberOfRows === 1) {
      return "50%";
    } else {
      return "95%";
    }
  }};

  min-height: 1.4em;
  overflow: hidden;
  border-radius: ${(props) => {
    switch (props.alignmentPosition) {
      case AlignmentPosition.Left:
        return "1.5em 0 0 1.5em";
      case AlignmentPosition.Right:
        return "0 1.5em 1.5em 0";
      default:
        return "0";
    }
  }};

  margin: ${(props) => {
    switch (props.alignmentPosition) {
      case AlignmentPosition.Left:
        return "0.00em  0.00em  0.00em 0.5em";
      default:
        return "0em  0em  0em  0em";
    }
  }};

  align-self: end;
  background-color: ${(props) => {
    const rgb = ColorService.hexToRgb(props.bgcolor);

    if (props.isHovered) {
      return `rgba(${rgb}, 0.99)`;
    }

    if (props.isSelected) {
      return `rgba(${rgb}, 0.75)`;
    }

    return `rgba(${rgb}, 0.45)`;
  }};

  transition: background-color 0.2s ease;
  user-select: none;
`;

interface DayBoxProps {
  date: number;
  dayData: EmployeeInfo[];
  numberOfRows: number;
  selectedEmployeeId?: string;
  hoveredEmployeeId?: string;
}

const DayBox: React.FC<DayBoxProps> = ({
  date,
  dayData,
  numberOfRows,
  selectedEmployeeId,
  hoveredEmployeeId,
}) => {
  const sortedDayData = [...dayData].sort(
    (a, b) => (a.row || 0) - (b.row || 0)
  );

  return (
    <StyledDayBox numberOfRows={numberOfRows} data-testid="day-box">
      {sortedDayData.map((employee) => (
        <StyledLine
          bgcolor={generateColorFromName(employee.name)}
          key={employee.id}
          row={employee.row}
          numberOfRows={numberOfRows}
          alignmentPosition={employee.positonRounding}
          isSelected={selectedEmployeeId === employee.id}
          isHovered={hoveredEmployeeId === employee.id}
          data-testid={`employee-line-${employee.id}`}
        >
          <StyledAvatar
            background={generateColorFromName(employee.name)}
            name={employee.name}
            photo={""}
            size={2}
            isVisible={employee.positonRounding == 1}
            data-testid={`employee-avatar-${employee.id}`}
          />
        </StyledLine>
      ))}
    </StyledDayBox>
  );
};

export default DayBox;
