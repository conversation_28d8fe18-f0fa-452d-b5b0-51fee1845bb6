import { createContext } from "react";

export interface User {
  userId: string | undefined;
  email: string | undefined;
  hasSignedIn: boolean;
  workTimeRoleName?: string;
}

const initialUserState: User = {
  userId: undefined,
  email: undefined,
  hasSignedIn: false,
  workTimeRoleName: undefined,
};

interface UserContextType {
  user: User;
  setUser: (user: User) => void;
  resetUser: () => void;
}

export const AuthContext = createContext<UserContextType>({
  user: initialUserState,
  setUser: (user: User) => {},
  resetUser: () => {},
});
