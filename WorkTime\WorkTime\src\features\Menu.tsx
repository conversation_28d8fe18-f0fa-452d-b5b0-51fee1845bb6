import { Ref, forwardRef, useContext, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useAppDispatch } from "../app/hooks";
import { onClearState } from "../app/store";
import logoutImg from "../assets/images/menu/logout.png";
import ArrowTip from "../components/ArrowTip";
import { SenderaUserDTO } from "../models/DTOs/SenderaUserDTO";
import {
  getSenderaUser,
  logout,
} from "../services/authentication/authenticationService";
import { initCompany } from "../services/companies/companiesService";
import Translator from "../services/language/Translator";
import { useMenu } from "./MenuContext";
import { AuthContext } from "./authentication/AuthContext";
import { CompanyContext } from "./companies/CompanyContext";
import EditCompany from "./companies/EditCompany";
import NewEmployeeSideMenu from "./employees/newEmployee/NewEmployeeSideMenu";
import Absence from "./events/EventsToggleMenu";

const StyledMenu = styled.div<{ isOpen: boolean }>`
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 25rem;
  background: var(--menu-background-color);
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
  transform: ${({ isOpen }) => (isOpen ? "translateX(0)" : "translateX(100%)")};
  transition: transform 0.4s ease-in;
  z-index: 1001;
  @media (max-width: 800px) {
    width: 13rem;
  }
`;

const NavContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  top: 0;
  width: 100%;
  margin: 0;
  background-color: var(--menu-header-background-color);
  padding: 1rem 0.6rem 1rem 0.6rem;
  box-sizing: border-box;
  height: 5rem;
`;

const LogOutImage = styled.img`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
`;

const StyledGrid = styled.div`
  display: grid;

  @media (max-width: 800px) {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 1em;
  box-sizing: border-box;
  height: 93%;
`;

const UserContainer = styled.div`
  color: var(--user-container-color);
  margin-right: 3rem;
`;

const MyCompaniesContainer = styled.div`
  color: var(--companies-container-color);
  margin-right: 4rem;
  font-size: 1.3rem;
  margin-left: -1rem;
`;

const CompanyName = styled.div`
  font-size: 1rem;
  display: flex;
  justify-content: center;
  text-align: center;
  margin-right: 2rem;
  margin-left: 2rem;
`;

interface MenuProps {
  isOpen: boolean;
  onToggleMenu: () => void;
}

const Menu = forwardRef<HTMLDivElement, MenuProps>(
  ({ isOpen }, ref: Ref<HTMLDivElement>) => {
    const { currentPage, closeMenu, menuOpenedFrom } = useMenu();

    const { company, setCompany, resetCompany } = useContext(CompanyContext);

    const navigate = useNavigate();

    const { resetUser } = useContext(AuthContext);
    const [senderaUser, setSenderaUser] = useState<SenderaUserDTO>();

    const companyPages = ["/company-structure", "/payrolls"];
    const absencePages = ["/attendance", "/onboardingdocument"];
    const employeePages = ["/employees"];

    const [activeMenu, setActiveMenu] = useState<string>("");
    const [goBack, setGoBack] = useState(false);
    const location = useLocation();
    const dispatch = useAppDispatch();

    useEffect(() => {
      const initializeData = async () => {
        const companyData = await initCompany();
        setCompany(companyData);
        await getSenderaUserData();
      };

      initializeData();
    }, [setCompany]);

    const getSenderaUserData = async () => {
      try {
        await getSenderaUser().then((response: SenderaUserDTO) => {
          setSenderaUser(response);
        });
      } catch (error) {}
    };

    const handleButtonClickFromProfile = (button: string) => {
      setActiveMenu(button);
      setGoBack(false);
    };

    const handleArrowClick = () => {
      if (activeMenu === "myCompanies") {
        setGoBack(true);
      } else {
        closeMenu();
      }
    };

    const handleLougOutClick = () => {
      handleLogout();
    };

    const handleLogout = () => {
      try {
        logout();
        dispatch(onClearState());
      } finally {
        resetUser();
        resetCompany();
        navigate("/auth/login");
        closeMenu();
      }
    };

    return (
      <StyledMenu isOpen={isOpen} ref={ref} data-testid="menu">
        <NavContainer data-testid="menu-nav-container">
          <ArrowTip
            direction={activeMenu === "myCompanies" ? "left" : "right"}
            onClick={handleArrowClick}
            data-testid="menu-arrow-tip"
          />
          {menuOpenedFrom === "companyLabel" ? (
            location.pathname !== "/" ? (
              <CompanyName data-testid="menu-company-name">
                {company?.name}
              </CompanyName>
            ) : (
              <></>
            )
          ) : (
            <>
              {absencePages.some((p) => currentPage.startsWith(p)) ? (
                <Translator
                  getString="Absence request"
                  data-testid="menu-absence-title"
                />
              ) : employeePages.some((p) => currentPage.startsWith(p)) ? (
                <UserContainer data-testid="menu-user-container">
                  {activeMenu !== "myCompanies" ? (
                    <>
                      <span data-testid="menu-user-name">
                        {senderaUser?.firstName} {senderaUser?.secondName}{" "}
                        {senderaUser?.lastName}
                      </span>
                      <br />
                      <span data-testid="menu-user-email">
                        {senderaUser?.email}
                      </span>
                    </>
                  ) : (
                    <MyCompaniesContainer data-testid="menu-companies-container">
                      <Translator
                        getString="My Companies"
                        data-testid="menu-companies-title"
                      />
                    </MyCompaniesContainer>
                  )}
                </UserContainer>
              ) : location.pathname !== "/" ? (
                <CompanyName data-testid="menu-company-name">
                  {company?.name}
                </CompanyName>
              ) : (
                <></>
              )}
            </>
          )}
          <LogOutImage
            onClick={handleLougOutClick}
            src={logoutImg}
            data-testid="menu-logout-button"
          />
        </NavContainer>
        <ContentContainer data-testid="menu-content-container">
          <StyledGrid data-testid="menu-grid">
            {menuOpenedFrom === "companyLabel" ? (
              <EditCompany data-testid="menu-edit-company" />
            ) : company?.id !== "" &&
              companyPages.some((p) => p === currentPage) ? (
              <EditCompany data-testid="menu-edit-company" />
            ) : absencePages.some((p) => currentPage.startsWith(p)) ? (
              <Absence data-testid="menu-absence" />
            ) : (
              <NewEmployeeSideMenu data-testid="menu-profile-side-menu" />
            )}
          </StyledGrid>
        </ContentContainer>
      </StyledMenu>
    );
  }
);

export default Menu;
