import { Action, AnyAction, Reducer } from "redux";
import { AppThunk, RootState } from "../../app/store";
import { NomenclatureDTO } from "../../models/DTOs/nomenclatures/NomenclatureDTO";
import { ThunkAction } from "@reduxjs/toolkit";


interface DepartmentsState {
  departmentOptions: NomenclatureDTO[];
}

const initialDepartmentsState: DepartmentsState = {
  departmentOptions: [],
};

interface LoadDepartmentOptionsAction {
  type: "LOAD_DEPARTMENT_OPTIONS";
  options: NomenclatureDTO[];
}

export const departmentsReducer: Reducer<DepartmentsState> = (
  state = initialDepartmentsState,
  incomingAction: Action
) => {
  const action = incomingAction as LoadDepartmentOptionsAction;

  switch (action.type) {
    case "LOAD_DEPARTMENT_OPTIONS": {
      return { ...state, departmentOptions: action.options };
    }
    default:
      return state;
  }
};

const loadDepartmentOptionsAction = (
  options: NomenclatureDTO[]
): ThunkAction<void, RootState, unknown, AnyAction> => {
  return (dispatch) => {
    dispatch({ type: "LOAD_DEPARTMENT_OPTIONS", payload: options });
  };
};

export const loadDepartmentOptions = (): AppThunk<
  void,
  LoadDepartmentOptionsAction
> => {
  return async (dispatch) => {
    try {
      const response = await fetch('/api/departments'); // Replace with actual API endpoint
      const data: NomenclatureDTO[] = await response.json();
      dispatch(loadDepartmentOptionsAction(data));
    } catch (error) {
      console.error('Failed to load department options:', error);
    }
  };
};

export const selectDepartmentOptions = (state: RootState) =>
  state.departments;

interface ContractTypesState {
  contractTypeOptions: NomenclatureDTO[];
}

const initialContractTypesState: ContractTypesState = {
  contractTypeOptions: [],
};

interface LoadContractTypeOptionsAction {
  type: "LOAD_CONTRACT_TYPE_OPTIONS";
  options: NomenclatureDTO[];
}

export const contractTypesReducer: Reducer<ContractTypesState> = (
  state = initialContractTypesState,
  incomingAction: Action
) => {
  const action = incomingAction as LoadContractTypeOptionsAction;

  switch (action.type) {
    case "LOAD_CONTRACT_TYPE_OPTIONS": {
      return { ...state, contractTypeOptions: action.options };
    }
    default:
      return state;
  }
};


const loadContractTypeOptionsAction = (
  options: NomenclatureDTO[]
): ThunkAction<void, RootState, unknown, AnyAction> => {
  return (dispatch) => {
    dispatch({ type: "LOAD_CONTRACT_TYPE_OPTIONS", payload: options });
  };
};

export const loadContractTypeOptions = (): AppThunk<
  void,
  LoadContractTypeOptionsAction
> => {
  return async (dispatch) => {
    try {
      const response = await fetch('/api/contract-types'); // Replace with actual API endpoint
      const data: NomenclatureDTO[] = await response.json();
      dispatch(loadContractTypeOptionsAction(data));
    } catch (error) {
      console.error('Failed to load contract type options:', error);
    }
  };
};

export const selectContractTypeOptions = (state: RootState) =>
  state.contractTypes;

interface CategoriesState {
  categoryOptions: NomenclatureDTO[];
}

const initialCategoriesState: CategoriesState = {
  categoryOptions: [],
};

interface LoadCategoryOptionsAction {
  type: "LOAD_CATEGORY_OPTIONS";
  options: NomenclatureDTO[];
}

export const categoriesReducer: Reducer<CategoriesState> = (
  state = initialCategoriesState,
  incomingAction: Action
) => {
  const action = incomingAction as LoadCategoryOptionsAction;

  switch (action.type) {
    case "LOAD_CATEGORY_OPTIONS": {
      return { ...state, categoryOptions: action.options };
    }
    default:
      return state;
  }
};

export const loadCategoryOptionsAction = (
  data: NomenclatureDTO[]
): ThunkAction<void, RootState, unknown, AnyAction> => {
  return (dispatch) => {
    dispatch({ type: 'LOAD_CATEGORY_OPTIONS', payload: data });
  };
};


export const loadCategoryOptions = (): AppThunk<
  void,
  LoadCategoryOptionsAction
> => {
  return async (dispatch) => {
    try {
      const response = await fetch('/api/categories'); // Replace with actual API endpoint
      const data: NomenclatureDTO[] = await response.json();
      dispatch(loadCategoryOptionsAction(data));
    } catch (error) {
      console.error('Failed to load category options:', error);
    }
  };
};

export const selectCategoryOptions = (state: RootState) =>
  state.categories;

