import { useEffect, useState } from "react";
import styled from "styled-components";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const PersonalInfoSection = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: -.rem;
  width: 100%;
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  background-color: #45b6f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: #333;
`;

const DropdownContainer = styled(Container)`
  position: relative;
  flex: 1;
`;

const DropdownHeader = styled.div`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1.25rem 1.25rem 0.6rem;
  margin: 0.2rem 0 0.2rem 0;
  outline: none;
  color: var(--input-field-color);
  background: var(--input-field-background-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`;

const DropdownLabel = styled.label<{ hasValue: boolean }>`
  position: absolute;
  left: 1.25rem;
  transition: all 0.2s ease;
  pointer-events: none;
  color: var(--input-field-label-color);
  font-size: ${(props) => (props.hasValue ? "0.75rem" : "1rem")};
  top: ${(props) => (props.hasValue ? "0.5rem" : "1.25rem")};
`;

const DropdownArrow = styled.div<{ isOpen: boolean }>`
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  transition: transform 0.2s ease;
`;

const DropdownBody = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: ${(props) => (props.isOpen ? "block" : "none")};
  max-height: 200px;
  overflow-y: auto;
`;

const DropdownOption = styled.div`
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #eee;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const CopyableField = styled(Container)`
  position: relative;
  flex: 1;
`;

const CopyButton = styled.button`
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  z-index: 10;
`;

const CopyIcon = styled(Image)`
  width: 16px;
  height: 16px;
`;

const EmployeeNumberDisplay = styled.div`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1.25rem 1.25rem 0.6rem;
  margin: 0.2rem 0 0.2rem 0;
  outline: none;
  color: var(--input-field-color);
  background: var(--input-field-background-color-read-only);
  border: 0.16rem solid white;
  position: relative;
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
`;

const EmployeeNumberLabel = styled.label`
  position: absolute;
  left: 1.25rem;
  top: 0.5rem;
  font-size: 0.75rem;
  color: var(--input-field-label-color);
  pointer-events: none;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: any;
}

interface FormData {
  firstName: string;
  lastName: string;
  familyName: string;
  egn: string;
  birthDate: string;
  contractType: string;
  birthPlace: string;
  iban: string;
  email: string;
  employeeNumber: string;
}

const NewPersonalData = ({ onValidation, data = {} }: Props) => {
  const [formData, setFormData] = useState<FormData>({
    firstName: data.firstName || "",
    lastName: data.lastName || "",
    familyName: data.familyName || "",
    egn: data.egn || "",
    birthDate: data.birthDate || "",
    contractType: data.contractType || "",
    birthPlace: data.birthPlace || "",
    iban: data.iban || "",
    email: data.email || "",
    employeeNumber: data.employeeNumber || "12345678",
  });

  const [isContractTypeDropdownOpen, setIsContractTypeDropdownOpen] =
    useState(false);

  const contractTypeOptions = [
    { value: "permanent", label: "Трудов договор" },
    { value: "temporary", label: "Срочен договор" },
    { value: "freelance", label: "Граждански договор" },
  ];

  useEffect(() => {
    const isValid =
      formData.firstName.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.egn.trim() !== "" &&
      formData.birthDate.trim() !== "";

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: FormData) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleContractTypeSelect = (value: string) => {
    setFormData((prev: FormData) => ({
      ...prev,
      contractType: value,
    }));
    setIsContractTypeDropdownOpen(false);
  };

  const getContractTypeLabel = () => {
    const option = contractTypeOptions.find(
      (opt) => opt.value === formData.contractType
    );
    return option ? option.label : "";
  };

  const handleCopyEmployeeNumber = async () => {
    try {
      await navigator.clipboard.writeText(formData.employeeNumber);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  return (
    <FormContainer>
      {/* Personal Info Section - Name fields */}
      <PersonalInfoSection>
        <FormField>
          <Textbox
            name="firstName"
            label="Име"
            value={formData.firstName}
            handleChange={handleChange}
          />
        </FormField>

        <FormField>
          <Textbox
            name="lastName"
            label="Презиме"
            value={formData.lastName}
            handleChange={handleChange}
          />
        </FormField>

        <FormField>
          <Textbox
            name="familyName"
            label="Фамилия"
            value={formData.familyName}
            handleChange={handleChange}
          />
        </FormField>
      </PersonalInfoSection>

      {/* Main Data Section */}
      <SectionHeader>
        <SectionIcon>ℹ️</SectionIcon>
        <SectionTitle>Основни данни</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="egn"
            label="ЕГН / ЛНЧ"
            value={formData.egn}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="birthDate"
            label="Дата на раждане"
            value={formData.birthDate}
            handleChange={handleChange}
            placeholder="17.05.1985"
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <DropdownContainer>
            <DropdownHeader
              onClick={() =>
                setIsContractTypeDropdownOpen(!isContractTypeDropdownOpen)
              }
            >
              <DropdownLabel hasValue={!!formData.contractType}>
                Тип договор
              </DropdownLabel>
              <span
                style={{ marginTop: formData.contractType ? "0.5rem" : "0" }}
              >
                {getContractTypeLabel()}
              </span>
              <DropdownArrow isOpen={isContractTypeDropdownOpen} />
            </DropdownHeader>
            <DropdownBody isOpen={isContractTypeDropdownOpen}>
              {contractTypeOptions.map((option) => (
                <DropdownOption
                  key={option.value}
                  onClick={() => handleContractTypeSelect(option.value)}
                >
                  {option.label}
                </DropdownOption>
              ))}
            </DropdownBody>
          </DropdownContainer>
        </FormField>
        <FormField>
          <Textbox
            name="birthPlace"
            label="Място на раждане"
            value={formData.birthPlace}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="iban"
            label="IBAN"
            value={formData.iban}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>{/* Empty field to maintain layout */}</FormField>
      </FormRow>

      {/* Profile Creation Data Section */}
      <SectionHeader>
        <SectionIcon>👤</SectionIcon>
        <SectionTitle>Данни за създаване на профил</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="email"
            label="E-mail"
            value={formData.email}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <CopyableField>
          <EmployeeNumberDisplay>
            <EmployeeNumberLabel>Служебен №</EmployeeNumberLabel>
            <span style={{ marginTop: "0.5rem" }}>
              {formData.employeeNumber}
            </span>
          </EmployeeNumberDisplay>
          <CopyButton onClick={handleCopyEmployeeNumber} type="button">
            <CopyIcon src={copyIcon} alt="Copy" />
          </CopyButton>
        </CopyableField>
      </FormRow>
    </FormContainer>
  );
};

export default NewPersonalData;
