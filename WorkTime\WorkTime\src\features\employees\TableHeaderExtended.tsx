import styled from "styled-components";
import ComboboxMultipleChoices from "../../components/Combobox/ComboboxMultipleChoices";
import BaseFilter from "../../components/Table/BaseFilter";
import { ColumnDefinitionType } from "../../components/Table/Table";
import { EmployeeListView } from "./EmployeesListView";

interface TableHeaderExtendedProps {
  columns?: Array<
    ColumnDefinitionType<EmployeeListView, keyof EmployeeListView>
  >;
  departmentOptions: {
    identifier: string;
    name?: string;
    description?: string;
  }[];
  categoryOptions: {
    identifier: number;
    name?: string;
    description?: string;
  }[];
  typeOfAppointmentOptions: {
    identifier: number;
    description?: string;
  }[];
  selectedDepartments: string[];
  selectedCategories: string[];
  selectedTypeOfAppointment: string[];
  onDepartmentChange: (selected: any[]) => void;
  onCategoryChange: (selected: any[]) => void;
  onTypeOfAppointmentChange: (selected: any[]) => void;
  onTextFilterChange: (columnKey: string, searchText: string) => void;
}

const TableRow = styled.tr`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  margin-bottom: -1.2rem;
`;

const TableHeaderCell = styled.th`
  text-align: center;
  background-color: var(--table-row-color);
  color: var(--table-header-color);
  padding: 0.2rem;
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  &:first-child {
    border-radius: 1.625rem 0 0 0;
  }
  &:last-child {
    border-radius: 0 1.625rem 0 0;
    padding-right: 0.5rem;
  }
`;

const TableHeaderExtended = ({
  departmentOptions,
  categoryOptions,
  typeOfAppointmentOptions,
  selectedDepartments,
  selectedCategories,
  selectedTypeOfAppointment,
  onDepartmentChange,
  onCategoryChange,
  onTypeOfAppointmentChange,
  onTextFilterChange,
}: TableHeaderExtendedProps): JSX.Element => {
  return (
    <thead data-testid="table-header-extended">
      <TableRow data-testid="table-header-row">
        <TableHeaderCell data-testid="personal-data-header">
          <BaseFilter
            data-testid="personal-data-filter"
            showSort={false}
            column={"personalData"}
            placeholder={"strSearchByPersonalData"}
            onFilterChange={onTextFilterChange}
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="position-header">
          <BaseFilter
            data-testid="position-filter"
            showSort={false}
            column={"position"}
            placeholder={"strSearchByPosition"}
            onFilterChange={onTextFilterChange}
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="departments-header">
          <ComboboxMultipleChoices
            data-testid="departments-combobox"
            options={departmentOptions.map((option) => ({
              id: option.identifier.toString(),
              label: option.name || "",
              value: option.identifier.toString(),
            }))}
            column={"department"}
            selectedValues={selectedDepartments}
            onChange={onDepartmentChange}
            placeholder="strDepartments"
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="contract-type-header">
          <ComboboxMultipleChoices
            options={typeOfAppointmentOptions.map((option) => ({
              id: option.identifier,
              label: option.description || "",
              value: option.identifier,
            }))}
            column={"typeOfAppointment"}
            selectedValues={selectedTypeOfAppointment}
            onChange={onTypeOfAppointmentChange}
            placeholder="Type of appointment"
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="category-header">
          <ComboboxMultipleChoices
            data-testid="category-combobox"
            options={categoryOptions.map((option) => ({
              id: option.identifier,
              label: option.name || "",
              value: option.identifier,
            }))}
            column={"category"}
            selectedValues={selectedCategories}
            onChange={onCategoryChange}
            placeholder="strCategories"
          />
        </TableHeaderCell>
      </TableRow>
    </thead>
  );
};

export default TableHeaderExtended;
