import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Alert from "../../components/Inputs/Alert";
import { confirmEmail } from "../../services/authentication/authenticationService";

const ConfirmEmail = () => {
  const [isConfirmed, setIsConfirmed] = useState<boolean | undefined>();
  const location = useLocation();
  const navigate = useNavigate();

  const queryParams = new URLSearchParams(location.search);
  const userId = queryParams.get("userId");
  const code = queryParams.get("code");

  useEffect(() => {
    if (userId && code) {
      confirmEmail(userId, code)
        .then(() => {
          setIsConfirmed(true);
          setTimeout(() => navigate("/auth/login"), 3000);
        })
        .catch(() => {
          setIsConfirmed(false);
        });
    }
  }, [userId, code, navigate]);

  const handleConfirmation = () => {
    if (isConfirmed === undefined)
      return (
        <Alert
          type="warning"
          message="Confirming E-mail"
          data-testid="confirm-email-alert"
        />
      );
    else if (isConfirmed) {
      return (
        <Alert
          type="success"
          message="E-mail confirmed successfully"
          data-testid="confirm-email-alert"
        />
      );
    } else
      return (
        <Alert
          type="error"
          message="E-mail was not confirmed!"
          data-testid="confirm-email-alert"
        />
      );
  };

  return <>{handleConfirmation()}</>;
};

export default ConfirmEmail;
