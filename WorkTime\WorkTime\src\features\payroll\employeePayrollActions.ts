import { Action, Reducer } from "redux";
import { AppThunk, RootState } from "../../app/store";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import {
  authenticatedGet
} from "../../services/connectionService";

interface EmployeePayrollsState {
  employeePayrolls: EmployeePayrollDTO[];
}

interface LoadEmployeePayrollsAction {
  type: "LOAD_EMPLOYEE_PAYROLLS";
  employeePayrolls: EmployeePayrollDTO[];
}

type KnownActions = LoadEmployeePayrollsAction;

const loadEmployeePayrollsAction = (employeePayrolls: EmployeePayrollDTO[]): LoadEmployeePayrollsAction => ({
  type: "LOAD_EMPLOYEE_PAYROLLS",
  employeePayrolls,
});

export const actionCreators = {
  onEmployeePayrollsLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      companyId && authenticatedGet<EmployeePayrollDTO[]>(
        `employee-payrolls?companyId=${companyId}`
      ).then((employeePayrolls) => {
        dispatch(loadEmployeePayrollsAction(employeePayrolls));
      });
    };
  },
};

export const { onEmployeePayrollsLoaded} =
  actionCreators;

const initialState = {
  employeePayrolls: [],
} as EmployeePayrollsState;

export const reducer: Reducer<EmployeePayrollsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "LOAD_EMPLOYEE_PAYROLLS":
      return {
        ...state,
        employeePayrolls: [...incomingAction.employeePayrolls],
      };
    default:
      return state;
  }
};

export const selectEmployeePayrolls = (state: RootState) => state.employeePayrolls; 