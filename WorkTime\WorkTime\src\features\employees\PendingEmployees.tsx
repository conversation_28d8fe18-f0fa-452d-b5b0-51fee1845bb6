import { MouseEvent, useContext, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import {
  onTRZEmployeesLoaded,
  selectEmployees,
  onEmployeesImported,
  onTRZEmployeeImported,
} from "./employeesActions";
import Button from "../../components/Inputs/Button";
import Container from "../../components/Container";
import Table, { ColumnDefinitionType } from "../../components/Table/Table";
import { CompanyContext } from "../companies/CompanyContext";
import { EmployeeListView } from "./EmployeesListView";
import Avatar from "../../components/Table/Avatar";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { styled } from "styled-components";
import trzImage from "../../assets/images/trzIcon.png";
import Label from "../../components/Inputs/Label";
import { useNavigate } from "react-router-dom";
import { authenticatedPost } from "../../services/connectionService";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";

const ImportEmployeesContainer = styled(Container)`
  display: grid;
  justify-content: center;
`;

const ImportAllEmployeesButton = styled(Container)<{
  importImage: string;
}>`
  width: 35%;
  background-color: var(--import-trz-employees-background-color);
  color: var(--import-trz-employees-color);
  font-size: 1rem;
  margin: auto;
  background-image: url(${(p) => p.importImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 3.1rem;
  background-position: left 1rem center;
  cursor: default;
  padding: 0.9rem;
  border-radius: 2rem;
`;

const ImportButton = styled(Button)`
  height: 1.95rem;
  cursor: pointer;
  float: right;
  padding: 0.5rem 1.5rem 1.9rem 1.5rem;
  background-color: var(--import-trz-employees-button-color);
  margin: auto;
`;

const ImportLabel = styled(Label)`
  align-items: center;
  margin-left: 4rem;
  line-height: 2.5rem;
`;

const TRZEmployees = () => {
  const dispatch = useAppDispatch();
  const { company } = useContext(CompanyContext);
  const trzEmployees = useAppSelector(selectEmployees).trzEmployees;
  const [filteredEmployees, setFilteredEmployees] = useState(trzEmployees);
  const navigate = useNavigate();

  const columns: ColumnDefinitionType<
    EmployeeListView,
    keyof EmployeeListView
  >[] = [
    {
      key: "avatar",
      value: "",
    },
    {
      key: "name",
      value: "���",
    },
    {
      key: "egn",
      value: "���",
    },
    {
      key: "position",
      value: "�������",
    },
    {
      key: "department",
      value: "�����",
    },
    {
      key: "email",
      value: "Email",
    },
    {
      key: "phone",
      value: "�������",
    },
  ];

  useEffect(() => {
    company && company.id !== "" && dispatch(onTRZEmployeesLoaded(company.id));
  }, [dispatch, company]);

  useEffect(() => {
    setFilteredEmployees(trzEmployees);
  }, [trzEmployees]);

  const filterColumns = (columnKey: string, searchText: string) => {
    setFilteredEmployees(
      trzEmployees.filter((employee) =>
        ((employee as any)[columnKey] as string)
          ?.toLocaleLowerCase()
          .includes(searchText?.toLocaleLowerCase())
      )
    );
  };

  const filtertedData = filteredEmployees?.map((e) => {
    return {
      workTimeId: e.workTimeId,
      id: e.employeeId?.toString(),
      avatar: (
        <Avatar
          name={e.firstName ?? translate("Unknown Name")}
          photo={""}
          size={2}
          data-testid={`employee-avatar-${e.workTimeId}`}
        />
      ),
      name: [e.firstName, e.secondName, e.lastName]
        .filter((name) => name != null)
        .join(" "),
      egn: e.egn,
      email: e.email,
    } as EmployeeListView;
  });

  const handleImportEmployee = async (id: string) => {
    const employee = trzEmployees.find((e) => e.workTimeId == id);

    if (employee) {
      dispatch(onTRZEmployeeImported(employee, company.id));
      toast.success(translate("EmployeeImportedSuccessfully"));
      if (trzEmployees.length === 1) {
        navigate("/employees");
      }
    } else {
      toast.error(translate("Unknown error on importing employee!"));
    }
  };

  const handleImportEmployees = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    var payrolls = await authenticatedPost<PayrollDTO[]>(`payrolls`, {
      companyId: company.id,
    });

    dispatch(onEmployeesImported(payrolls));
    toast.success(translate("EmployeesImportedSuccessfully"));
    navigate("/employees");
  };

  const importEmployeeButton = (id: string) => (
    <Button
      label="Import"
      onClick={(e) => {
        e.preventDefault();
        handleImportEmployee(id);
      }}
      data-testid={`import-employee-button-${id}`}
    />
  );

  return (
    <ImportEmployeesContainer data-testid="import-employees-container">
      <ImportAllEmployeesButton
        importImage={trzImage}
        data-testid="import-all-employees-button"
      >
        <ImportLabel children="Import TRZ Data" data-testid="import-label" />
        <ImportButton
          label="Import"
          onClick={handleImportEmployees}
          data-testid="import-all-button"
        />
      </ImportAllEmployeesButton>
      <Table
        data={filtertedData}
        filterColumns={filterColumns}
        columns={columns}
        buttons={[{ renderButton: importEmployeeButton }]}
        data-testid="employees-table"
      />
    </ImportEmployeesContainer>
  );
};

export default TRZEmployees;
