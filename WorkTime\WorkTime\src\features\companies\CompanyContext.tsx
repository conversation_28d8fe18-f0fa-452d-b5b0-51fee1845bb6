import { createContext } from "react";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";

const initialCompanyState: CompanyDTO = {
  id: "",
  name: "",
  bulstat: "",
  userRegistrationCompanyId: 0,
  contactName: "",
};

interface CompanyContextType {
  company: CompanyDTO;
  setCompany: (company: CompanyDTO) => void;
  resetCompany: () => void;
}

export const CompanyContext = createContext<CompanyContextType>({
  company: initialCompanyState,
  setCompany: (company: CompanyDTO) => {},
  resetCompany: () => {},
});
