import { ChangeEvent, useState } from "react";
import Textbox from "./Textbox";
import { isValidEmail, isValidUsername } from "../../services/emailService";

interface Props
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  value: string | number;
  validateUsername?: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  alertMessage?: string;
}

const EmailBox = ({
  handleChange,
  label,
  value,
  alertMessage,
  name,
  validateUsername = false,
  ...rest
}: Props) => {
  const [isValid, setIsValid] = useState(true);

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIsValid(
      validateUsername
        ? isValidUsername(e.currentTarget.value.trim())
        : isValidEmail(e.currentTarget.value.trim())
    );

    handleChange(e);
  };

  return (
    <Textbox
      data-testid="email-input"
      handleChange={handleEmailChange}
      label={label}
      value={value}
      name={name}
      {...rest}
      validation={{
        isValid: isValid,
        alertMessage: alertMessage ?? "Invalid Email",
      }}
    />
  );
};

export default EmailBox;
