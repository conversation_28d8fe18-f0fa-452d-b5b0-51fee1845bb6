import { Action, Reducer } from "redux";
import { AppThunk, RootState } from "../../app/store";
import { authenticatedGet } from "../../services/connectionService";
import { AbsencesAndHospitalsDTO } from "../../models/DTOs/attendance/AbsencesAndHospitalsDTO";

interface AttendanceState {
    leaves: AbsencesAndHospitalsDTO[];
}

interface LoadLeavesAction {
    type: "LOAD_Leaves";
    leaves: AbsencesAndHospitalsDTO[];
}

type KnownActions = LoadLeavesAction;

const loadLeavesAction = (leaves: AbsencesAndHospitalsDTO[]): LoadLeavesAction => ({
    type: "LOAD_Leaves",
    leaves,
});

export const actionCreators = {
    onLeavesLoaded: (date: Date, companyId: string): AppThunk<void, KnownActions> => {
        return async (dispatch: any) => {
            const formattedDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())).toISOString().split('T')[0];
    
            authenticatedGet<AbsencesAndHospitalsDTO[]>(
                `absences/monthly?date=${formattedDate}&companyId=${companyId}`
            ).then((leaves) => {
                dispatch(loadLeavesAction(leaves));
            });
        };
    },
};

export const {
    onLeavesLoaded,
} = actionCreators;

const initialState = {
    leaves: [],
} as AttendanceState;

export const reducer: Reducer<AttendanceState> = (
    state = initialState,
    action: Action
) => {
    const incomingAction = action as KnownActions;
    switch (incomingAction.type) {
        case "LOAD_Leaves":
            return {
                ...state,
                leaves: [...incomingAction.leaves],
            };
        default:
            return state;
    }
};

export const selectLeaves = (state: RootState) => state.attendance.leaves;