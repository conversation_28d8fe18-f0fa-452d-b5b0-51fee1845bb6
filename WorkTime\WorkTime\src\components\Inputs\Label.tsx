import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface LabelProps
  extends React.DetailedHTMLProps<
    React.LabelHTMLAttributes<HTMLLabelElement>,
    HTMLLabelElement
  > {
  children: string;
}

const LabelField = styled.label`
  color: var(--label-color);
  align-self: center;
`;

const Label = (props: LabelProps) => {
  const { children } = props;
  return (
    <LabelField {...props} data-testid="label-field">
      <Translator getString={children} data-testid="label-translator" />
    </LabelField>
  );
};

export default Label;
