import "./App.css";
import { LanguageProvider } from "./services/language/LanguageProvider";
import AppRouter from "./AppRouter";
import Layout from "./features/Layout";
import { AuthContext, User } from "./features/authentication/AuthContext";
import { useEffect, useState } from "react";
import { initUser } from "./services/authentication/authenticationService";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { CompanyContext } from "./features/companies/CompanyContext";
import { initCompany } from "./services/companies/companiesService";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { PermissionsProvider } from "./features/authorization/PermissionsProvider";
import { MenuProvider } from "./features/MenuContext";
import { EnumProvider } from "./features/EnumContext";
import { NomenclatureWithDescriptionProvider } from "./features/NomenclatureWithDescription";
import { DefaultLocationDataProvider } from "./features/DefaultLocationDataContext";
import { CompanyDTO } from "./models/DTOs/companies/CompanyDTO";

function App() {
  const [user, setUser] = useState<User>({
    email: undefined,
    hasSignedIn: false,
  });

  const [company, setCompany] = useState<CompanyDTO>({
    id: "",
    name: "",
    bulstat: "",
    userRegistrationCompanyId: 0,
    contactName: "",
  });

  const resetCompany = () => {
    setCompany({
      id: "",
      name: "",
      bulstat: "",
      userRegistrationCompanyId: 0,
      contactName: "",
    });
  };

  useEffect(() => {
    setUser(initUser());
  }, [setUser]);

  useEffect(() => {
    const initializeCompany = async () => {
      const companyData = await initCompany();
      setCompany(companyData);
    };

    initializeCompany();
  }, [setCompany]);

  return (
    <DefaultLocationDataProvider>
      <NomenclatureWithDescriptionProvider>
        <EnumProvider>
          <AuthContext.Provider
            value={{
              user: user,
              setUser: setUser,
              resetUser: () =>
                setUser({ email: undefined, hasSignedIn: false }),
            }}
          >
            <CompanyContext.Provider
              value={{
                company: company,
                setCompany: setCompany,
                resetCompany: resetCompany,
              }}
            >
              <PermissionsProvider>
                <GoogleOAuthProvider
                  clientId={import.meta.env.VITE_GOOGLE_ID ?? ""}
                >
                  <MenuProvider>
                    <LanguageProvider>
                      <div className="App">
                        <Layout />
                        <AppRouter />
                        <ToastContainer />
                      </div>
                    </LanguageProvider>
                  </MenuProvider>
                </GoogleOAuthProvider>
              </PermissionsProvider>
            </CompanyContext.Provider>
          </AuthContext.Provider>
        </EnumProvider>
      </NomenclatureWithDescriptionProvider>
    </DefaultLocationDataProvider>
  );
}

export default App;
