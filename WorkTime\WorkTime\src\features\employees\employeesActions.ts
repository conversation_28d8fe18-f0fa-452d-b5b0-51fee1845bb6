import { toast } from "react-toastify";
import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";
import { TRZEmployee } from "../../models/DTOs/trz/TRZEmployeeDTO";
import { CreateEmployeeForCompanyRequest } from "../../models/Requests/CreateEmployeeForCompanyRequest";
import { ImportTRZEmployeeRequest } from "../../models/Requests/ImportTRZEmployeeRequest";
import {
  authenticatedDelete,
  authenticatedGet,
  authenticatedPost,
} from "../../services/connectionService";
import { translate } from "../../services/language/Translator";

interface EmployeesState {
  employees: EmployeeDTO[];
  trzEmployees: TRZEmployee[];
}

interface AddEmployeeAction {
  type: "ADD_EMPLOYEE";
  employee: EmployeeDTO;
}

interface LoadEmployeesAction {
  type: "LOAD_EMPLOYEES";
  employees: EmployeeDTO[];
}

interface LoadTRZEmployeesAction {
  type: "LOAD_TRZEmployees";
  trzEmployees: TRZEmployee[];
}

interface DeleteEmployeeAction {
  type: "DELETE_EMPLOYEE";
  employeeId: string;
}
interface ImportTRZEmployeesAction {
  type: "IMPORT_TRZEmployees";
  trzEmployee: TRZEmployee;
}

interface ImportEmployeesAction {
  type: "IMPORT_EMPLOYEES";
  employees: EmployeeDTO[];
}

type KnownActions =
  | AddEmployeeAction
  | LoadEmployeesAction
  | LoadTRZEmployeesAction
  | DeleteEmployeeAction
  | ImportTRZEmployeesAction
  | ImportEmployeesAction
  | ClearStateAction;

const addEmployeeAction = (employee: EmployeeDTO): AddEmployeeAction => ({
  type: "ADD_EMPLOYEE",
  employee,
});

const loadEmployeesAction = (
  employees: EmployeeDTO[]
): LoadEmployeesAction => ({
  type: "LOAD_EMPLOYEES",
  employees,
});

const importEmployeesAction = (
  employees: EmployeeDTO[]
): ImportEmployeesAction => ({
  type: "IMPORT_EMPLOYEES",
  employees,
});

const loadTRZEmployeesAction = (
  trzEmployees: TRZEmployee[]
): LoadTRZEmployeesAction => ({
  type: "LOAD_TRZEmployees",
  trzEmployees,
});

const deleteEmployeeAction = (employeeId: string): DeleteEmployeeAction => ({
  type: "DELETE_EMPLOYEE",
  employeeId,
});

const importTrzEmployeeAction = (
  trzEmployee: TRZEmployee
): ImportTRZEmployeesAction => ({
  type: "IMPORT_TRZEmployees",
  trzEmployee,
});

export const actionCreators = {
  onEmployeeSaved: (
    createRequest: CreateEmployeeForCompanyRequest
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedPost<EmployeeDTO>(
        `companies/${createRequest.companyId}/employees`,
        createRequest
      ).then((employee) => {
        dispatch(addEmployeeAction(employee));

        toast.success(translate("EmployeeSuccessfullyCreated"));
      });
    };
  },
  onEmployeesImported: (
    payrolls: PayrollDTO[]
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(importEmployeesAction(payrolls.map((p) => p.employee)));
    };
  },
  onEmployeesLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<EmployeeDTO[]>(`employees?companyId=${companyId}`).then(
        (employees) => {
          dispatch(loadEmployeesAction(employees));
        }
      );
    };
  },
  onTRZEmployeesLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<TRZEmployee[]>(
        `trz/employees?companyId=${companyId}`
      ).then((trzEmployees) => {
        dispatch(loadTRZEmployeesAction(trzEmployees));
      });
    };
  },
  onEmployeeDelete: (employeeId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedDelete("employees/delete-employee?EmployeeId=", employeeId)
        .then(() => {
          dispatch(deleteEmployeeAction(employeeId));
        })
        .catch((er) => {
          alert(er);
        });
    };
  },
  onTRZEmployeeImported: (
    trzEmployee: TRZEmployee,
    companyId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const importEmployeeRequest = {
        worktimeId: trzEmployee.workTimeId,
        companyid: companyId,
      } as ImportTRZEmployeeRequest;
      authenticatedPost<TRZEmployee>("employees/trz", importEmployeeRequest)
        .then((importedEmployee) => {
          dispatch(importTrzEmployeeAction(importedEmployee));
        })
        .catch((error) => {
          console.error(error);
        });
    };
  },
};
export const {
  onEmployeeSaved,
  onEmployeesLoaded,
  onTRZEmployeesLoaded,
  onEmployeeDelete,
  onTRZEmployeeImported,
  onEmployeesImported,
} = actionCreators;

const initialState = {
  employees: [],
  trzEmployees: [],
} as EmployeesState;

export const reducer: Reducer<EmployeesState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "ADD_EMPLOYEE":
      return {
        ...state,
        employees: [...state.employees, incomingAction.employee],
      };
    case "LOAD_EMPLOYEES":
      return {
        ...state,
        employees: [...incomingAction.employees],
      };
    case "LOAD_TRZEmployees":
      return {
        ...state,
        trzEmployees: [...incomingAction.trzEmployees],
      };
    case "DELETE_EMPLOYEE":
      const employeeId = incomingAction.employeeId;
      return {
        ...state,
        employees: [
          ...state.employees.filter((p) => p.workTimeId !== employeeId),
        ],
      };
    case "IMPORT_TRZEmployees":
      var trzEmployeeId = incomingAction.trzEmployee.workTimeId;
      return {
        ...state,
        trzEmployees: [
          ...state.trzEmployees.filter((e) => e.workTimeId !== trzEmployeeId),
        ],
      };
    case "IMPORT_EMPLOYEES":
      const employees = incomingAction.employees;
      return {
        ...state,
        employees: [...state.employees, ...employees],
        trzEmployees: [
          ...state.trzEmployees.filter(
            (e) =>
              !employees.some(
                (employee) => employee.workTimeId === e.workTimeId
              )
          ),
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectEmployees = (state: RootState) => state.employees;
