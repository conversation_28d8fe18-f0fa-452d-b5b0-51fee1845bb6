import React, { ChangeEvent, useState } from "react";
import styled from "styled-components";
import Search from "../../assets/images/table/search.png";
import searchHover from "../../assets/images/table/searchHover.png";
import Container from "../Container";
import Image from "../Image";
import Textbox from "../Inputs/Textbox";
interface BaseFilterProps
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  column: string;
}

const SearchInputWrapper = styled(Container)`
  position: relative;
  width: 100%;
`;

const SearchInput = styled(Textbox)`
  text-align: left;
  opacity: 1;
  font-family: Segoe UI;
  font-style: normal;
  font-weight: 400;
  border-radius: 1.625rem;
  border: 0.063rem;
  &:focus {
    outline: none;
    box-shadow: 0rem 0rem 0.125rem var(--search-input-box-shadow-color);
  }

  min-width: 8rem;
`;

const SearchIconStyled = styled(Image)`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  pointer-events: none;

  &:hover {
    background-image: url(${searchHover});
  }
`;

const SearchDiv = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0.313rem;
`;

export enum Direction {
  Ascending = -1,
  Descending = 1,
}

interface BaseFilterProps {
  column: string;
  placeholder: string;
  onFilterChange?: (column: string, value: string) => void;
  showSort?: boolean;
}

const BaseFilter = ({
  column,
  placeholder,
  onFilterChange,
}: BaseFilterProps): JSX.Element => {
  const [searchValue, setSearchValue] = useState("");

  const onSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    onFilterChange?.(column, value);
  };

  return (
    <SearchDiv data-testid="base-filter-container">
      <SearchInputWrapper data-testid="search-input-wrapper">
        <SearchInput
          data-testid="search-input"
          label={placeholder}
          value={searchValue}
          handleChange={onSearchChange}
        />
        <SearchIconStyled data-testid="search-icon" src={Search} size="small" />
      </SearchInputWrapper>
    </SearchDiv>
  );
};

export default BaseFilter;
