import { MouseEvent } from "react";
import { useNavigate } from "react-router";
import Button from "../../../components/Inputs/Button";

const Index = () => {
  const navigate = useNavigate();

  const handleCreatePayroll = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    navigate("/payroll/create");
  };

  return (
    <Button
      data-testid="create-payroll-button"
      label="strCreatePayroll"
      onClick={handleCreatePayroll}
    />
  );
};

export default Index;
