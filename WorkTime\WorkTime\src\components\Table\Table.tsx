import React, { useState } from "react";
import styled from "styled-components";
import { IEntity } from "../../models/DTOs/IEntity";
import Container from "../Container";
import { Direction } from "./Direction";
import SecondaryHeader from "./SecondaryHeader";
import TableFooter from "./TableFooter";
import TableHeader from "./TableHeader";
import TableRows, { ButtonCell } from "./TableRow";
import useTable from "./useTable";

export type ColumnDefinitionType<T, K extends keyof T> = {
  key: K;
  value: string;
};

interface TableProps<T, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  data: Array<T>;
  columns?: Array<ColumnDefinitionType<T, K>>;
  filterColumns?: (columnKey: string, searchText: string) => void;
  onRowClick?: (id: string) => void;
  buttons?: ButtonCell[];
  headerComponent?: JSX.Element;
  onSortChange?: (columnKey: string, direction: Direction) => void;
}

const TableWrapper = styled.table`
  border-collapse: collapse;
`;

const Wrapper = styled(Container)`
  display: table;
  margin: 0;
  padding: 0;
`;

const Table = <T extends IEntity, K extends keyof T>({
  data,
  columns,
  filterColumns,
  onRowClick,
  buttons,
  headerComponent,
  onSortChange,
}: TableProps<T, K>): JSX.Element => {
  const [page, setPage] = useState(1);
  const { slice, range, setSlice } = useTable(data, page, 15);

  return (
    <Wrapper data-testid="table-wrapper">
      <TableWrapper data-testid="table-table">
        {headerComponent ? (
          headerComponent
        ) : (
          <TableHeader
            columns={columns ?? []}
            data={slice}
            filterColumns={filterColumns ?? (() => {})}
            setTableData={setSlice}
          />
        )}
        <SecondaryHeader
          columns={columns ?? []}
          onSortChange={
            onSortChange as (column: string, direction: Direction) => void
          }
        />
        <TableRows
          data={slice}
          columns={columns ?? []}
          onRowClick={onRowClick}
          buttons={buttons}
        />
      </TableWrapper>
      <TableFooter
        data-testid="table-footer"
        range={range}
        slice={slice}
        setPage={setPage}
        page={page}
        setCurrentPage={setPage}
      />
    </Wrapper>
  );
};

export default Table;
