import { EmployeeDTO } from "../employees/EmployeeDTO";
import { IEntity } from "../IEntity";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";
import { ContractDTO } from "./ContractDTO";

export interface PayrollDTO extends IEntity {
  employee: EmployeeDTO;
  companyId: string;
  contract: ContractDTO;
  contractNumber: string;
  position: NomenclatureDTO;
  structureLevelId: string;
}
