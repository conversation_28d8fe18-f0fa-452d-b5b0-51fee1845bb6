import {
  authenticatedPut,
  authenticatedGet,
} from "../../services/connectionService";
import { PersonalInformationDTO } from "../../models/DTOs/payrolls/PersonalInformationDTO";
import { EditEmployeesRequest } from "../../models/Requests/Employees/EditEmployeeRequest";

export const profileService = {
  onProfileSaved: async (profile: PersonalInformationDTO) => {
    try {
      const isSuccess = await authenticatedPut<boolean>(
        `employees/${profile.employee.id}`,
        {
          employeeInfo: profile,
        } as EditEmployeesRequest
      );

      return isSuccess;
    } catch (error) {
      throw error;
    }
  },

  onProfileLoaded: async (id?: string) => {
    try {
      const profile = await authenticatedGet<PersonalInformationDTO>(
        `employees/${id}`
      );

      return profile;
    } catch (error) {
      throw error;
    }
  },

  onUserProfileLoaded: async (userId?: string) => {
    try {
      const profile = await authenticatedGet<PersonalInformationDTO>(
        `user-employee/${userId}`
      );
      return profile;
    } catch (error) {
      throw error;
    }
  },
};

export const getBirthDateFromEGN = (egn: string | undefined): string => {
  if (!egn || egn.length !== 10 || !/^\d{10}$/.test(egn)) {
    return "";
  }

  const yearPart = parseInt(egn.substring(0, 2), 10);
  let monthPart = parseInt(egn.substring(2, 4), 10);
  const dayPart = parseInt(egn.substring(4, 6), 10);

  let year: number;

  if (monthPart > 40) {
    year = 2000 + yearPart;
    monthPart -= 40;
  } else if (monthPart > 20) {
    year = 1800 + yearPart;
    monthPart -= 20;
  } else {
    year = 1900 + yearPart;
  }

  const birthDate = new Date(year, monthPart - 1, dayPart);
  if (
    birthDate.getDate() !== dayPart ||
    birthDate.getMonth() + 1 !== monthPart ||
    birthDate.getFullYear() !== year
  ) {
    return "";
  }

  const formattedDate = birthDate
    .toLocaleDateString("bg-BG", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
    .replace(/\//g, ".");

  return formattedDate;
};
