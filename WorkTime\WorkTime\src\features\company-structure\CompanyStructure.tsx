import officeImage from "../../assets/images/company-structure/office.png";
import departmentImage from "../../assets/images/company-structure/department.png";
import positionImage from "../../assets/images/company-structure/position.png";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import {
  onStructureLevelsLoaded,
  selectStructureLevels,
} from "./companyStructureActions";
import { useContext, useEffect } from "react";
import { StructureLevelDTO } from "../../models/DTOs/companyStructure/StructureLevelDTO";
import { CompanyContext } from "../companies/CompanyContext";
import styled from "styled-components";
import CompanyStructureCard from "./CompanyStructureCard";
import StructureLevelCard from "./StructureLevelCard";
import MainWindowContainer from "../../components/MainWindowContainer";

const borderThickness = 1;

const Tree = styled.div`
  overflow: scroll;
  & div {
    box-sizing: border-box;
    cursor: default;
  }
  & ul {
    box-sizing: border-box;
    position: relative;
    padding: 1em 0;
    white-space: nowrap;
    margin: 0 auto;
    text-align: center;
    &:after {
      content: "";
      box-sizing: border-box;
      display: table;
      clear: both;
    }
    & ul:before {
      content: "";
      box-sizing: border-box;
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: ${borderThickness / 2}px;
      border-left: ${borderThickness}px solid var(--company-structure-lines);
      width: 0;
      height: 1em;
    }
    & li {
      box-sizing: border-box;
      display: inline-block;
      vertical-align: top;
      text-align: center;
      list-style-type: none;
      position: relative;
      padding: 1em 0.5em 0;
      margin-left: -0.25em;
      margin-right: -0.25em;
      &:before,
      &:after {
        content: "";
        box-sizing: border-box;
        position: absolute;
        top: 0;
        right: 50%;
        border-top: ${borderThickness}px solid var(--company-structure-lines);
        width: 50%;
        height: 1em;
      }
      &:after {
        box-sizing: border-box;
        left: 50%;
        right: auto;
        margin-left: ${borderThickness / 2}px;
        border-left: ${borderThickness}px solid var(--company-structure-lines);
      }
      &:only-child:after,
      &:only-child:before {
        box-sizing: border-box;
        display: none;
      }
      &:only-child {
        padding-top: 0;
        box-sizing: border-box;
      }
      &:first-child:before,
      &:last-child:after {
        border: 0 none;
        box-sizing: border-box;
      }
      &:last-child:before {
        box-sizing: border-box;
        border-right: ${borderThickness}px solid var(--company-structure-lines);
        left: ${borderThickness / 2}px;
        border-radius: 0;
      }
    }
  }
`;

const CompanyStructure = () => {
  const dispatch = useAppDispatch();
  const structureLevels = useAppSelector(selectStructureLevels).structureLevels;
  const company = useContext(CompanyContext).company;

  useEffect(() => {
    dispatch(onStructureLevelsLoaded(company.id ?? ""));
  }, [dispatch, company]);

  const renderStructureLevel = (structureLevel: StructureLevelDTO) => {
    let typeImage = "";

    switch (structureLevel.typeId) {
      case 1:
        typeImage = officeImage;
        break;
      case 2:
        typeImage = departmentImage;
        break;
      case 3:
        typeImage = positionImage;
        break;
      default:
        typeImage = officeImage;
        break;
    }

    return (
      <li
        key={structureLevel.id}
        data-testid={`structure-level-${structureLevel.id}`}
      >
        <StructureLevelCard
          structureLevelName={structureLevel.name}
          imageSrc={typeImage}
          data-testid={`structure-level-card-${structureLevel.id}`}
        />
        {structureLevel.childStructureLevels &&
          structureLevel.childStructureLevels.length > 0 && (
            <ul data-testid={`child-structure-levels-${structureLevel.id}`}>
              {structureLevel.childStructureLevels &&
                structureLevel.childStructureLevels.map((child) =>
                  renderStructureLevel(child)
                )}
            </ul>
          )}
      </li>
    );
  };

  return (
    <MainWindowContainer>
      <Tree>
        <ul>
          <li key={company.id} data-testid={`company-${company.id}`}>
            <CompanyStructureCard
              companyName={company.name ?? ""}
              data-testid={`company-card-${company.id}`}
            />
            <ul data-testid="structure-levels">
              {structureLevels &&
                structureLevels.length > 0 &&
                structureLevels.map((structureLevel) =>
                  renderStructureLevel(structureLevel)
                )}
            </ul>
          </li>
        </ul>
      </Tree>
    </MainWindowContainer>
  );
};

export default CompanyStructure;
