import React from "react";
import { useContext } from "react";
import { PermissionsContext } from "./PermissionsContext";
import { CompanyContext } from "../companies/CompanyContext";
import { isUserPermitted } from "../../services/authorization/authorizationService";

interface Props {
  children: React.ReactNode | React.ReactNode[];
  requiredPermissions: string[];
}

const PermissionComponent: React.FC<Props> = ({
  children,
  requiredPermissions,
}) => {
  const { permissions } = useContext(PermissionsContext);
  const { company } = useContext(CompanyContext);

  if (isUserPermitted(requiredPermissions, permissions, company)) {
    return <>{children}</>;
  } else {
    return null;
  }
};

export default PermissionComponent;
