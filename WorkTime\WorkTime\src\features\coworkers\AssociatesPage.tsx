import { useEffect } from "react";
import { useMenu } from "../MenuContext";
import { useNavigate } from "react-router-dom";

const AssociatesPage = () => {
  const { changeView, toggleMenu } = useMenu();
  const navigate = useNavigate();

  useEffect(() => {
    toggleMenu();
    changeView("coworkers", "associates");
    navigate("/employees");
  }, []);

  return <></>;
};

export default AssociatesPage;
