import styled from "styled-components";
import { useLocation } from "react-router-dom";
import Menu from "./Menu";
import NavLinkButton from "../components/NavLinkButton";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "./authentication/AuthContext";
import Structure from "../assets/images/menu/structure.png";
import workers_active from "../assets/images/menu/menu_workers_active.svg";
import workers_inactive_hover from "../assets/images/menu/menu_workers_inactive_hover.svg";
import vacations_active from "../assets/images/menu/menu_vacations_active.svg";
import vacations_inactive_hover from "../assets/images/menu/menu_vacations_ inactive_hover.svg";
import import_trz_active from "../assets/images/menu/menu_import_trz_active.svg";
import import_trz_inactive_hover from "../assets/images/menu/menu_import_trz_inactive_hover.svg";

import { useAppDispatch, useAppSelector } from "../app/hooks";
import {
  onTRZEmployeesLoaded,
  selectEmployees,
} from "./employees/employeesActions";
import { CompanyContext } from "./companies/CompanyContext";

const NavContainer = styled.div`
  flex: 0 1 auto;
  width: 100%;
  box-sizing: border-box;
  display: inline-flex;
  padding: 0.7rem;
  background-color: var(--app-background-color);
`;

interface Props {
  isMenuOpen: boolean;
  menuRef: React.RefObject<HTMLDivElement>;
  onClick: () => void;
}

const MainWindow = ({ isMenuOpen, menuRef, onClick }: Props) => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { user } = useContext(AuthContext);
  const { company } = useContext(CompanyContext);
  const trzEmployees = useAppSelector(selectEmployees).trzEmployees;
  const role = localStorage.getItem("LOCAL_STORAGE_WORKTIME_ROLE_NAME");
  const isOwner = role === "grOwner";

  useEffect(() => {
    setIsAuthenticated(!!user.email);
  }, [user]);

  useEffect(() => {
    if (company?.id) {
      dispatch(onTRZEmployeesLoaded(company.id));
    }
  }, [dispatch, company?.id]);

  const shouldShowMenu =
    isAuthenticated && location.pathname !== "/" && user.hasSignedIn;

  return (
    <>
      {shouldShowMenu && (
        <NavContainer data-testid="nav-container">
          {/* <NavLinkButton
            to="/company-structure"
            imageSrc={Structure}
            label="Structure"
            currentpath={location.pathname}
          /> */}
          {
            <NavLinkButton
              to={`/${company?.id}/employees`}
              imageSrc={workers_active}
              hoverImageSrc={workers_inactive_hover}
              label="Employees"
              currentpath={location.pathname}
              data-testid="employees-nav-button"
            />
          }
          {
            <NavLinkButton
              to={`/${company?.id}/attendance`}
              imageSrc={vacations_active}
              hoverImageSrc={vacations_inactive_hover}
              label="Events"
              currentpath={location.pathname}
              data-testid="attendance-nav-button"
            />
          }
          {isOwner && trzEmployees.length > 0 && (
            <NavLinkButton
              to={`/${company?.id}/pending-employees`}
              imageSrc={import_trz_active}
              hoverImageSrc={import_trz_inactive_hover}
              label="Import"
              currentpath={location.pathname}
              data-testid="pending-employees-nav-button"
            />
          )}
        </NavContainer>
      )}
      <Menu
        isOpen={isMenuOpen}
        ref={menuRef}
        onToggleMenu={onClick}
        data-testid="menu"
      />
    </>
  );
};

export default MainWindow;
