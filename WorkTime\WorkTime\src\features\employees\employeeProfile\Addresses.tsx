import { useEffect, useState } from "react";
import styled from "styled-components";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import Image from "../../../components/Image";
import profileMan from "../../../assets/images/profile/profileMan.svg";

import editIcon from "../../../assets/images/profile/editNormal.svg";
import editIconHover from "../../../assets/images/profile/editHover.svg";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import Translator from "../../../services/language/Translator";

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin: 0.5rem 0;
  width: 100%;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 0;
  height: 4rem;
  width: 4rem;
`;

const EmployeeName = styled(Label)`
  text-align: left;
  font-weight: bold;
  opacity: 1;
  font-size: 1rem;
  word-wrap: normal;
  margin: 0;
`;

const DepartmentName = styled(Label)`
  font-size: 1rem;
  text-align: left;
  margin: 0;
  color: var(--profile-department-name-font-color);
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 30%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 70%;
`;

const EditButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isdisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background-image: url(${(p) => p.normalImage});
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: center;
  cursor: pointer;
  padding: 0;

  &:hover {
    background-image: url(${(p) => p.hoverImage});
  }
`;

const TopContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  gap: 1rem;
`;

const EmployeeInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
`;

const RightColumn = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 17%;
  height: 100%;
  margin-right: -2.3rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  border-radius: 2.2rem;
  justify-content: flex-start;
  align-items: flex-start;
`;

const MenuText = styled(Label)<{ isSelected?: boolean }>`
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  text-align: left;
  color: ${(props) =>
    props.isSelected ? "var(--profile-menu-text-hover-color)" : "#bdc4d6"};
  cursor: pointer;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
`;

const MenuItem = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0.5rem;
  padding-top: 0;
  align-items: flex-start;

  &:hover {
    ${MenuText} {
      color: var(--profile-department-name-font-color);
    }
  }
`;

const MenuLine = styled.div`
  width: 100%;
  height: 1px;
  background-color: #bdc4d6;
  opacity: 0.5;
`;

const ContainersWrapper = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 2rem;
  align-items: flex-start;
`;

const MainContentWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 85%;
  gap: 1rem;
`;

const MainContent = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;

  @media (max-width: 1200px) {
    flex-direction: column;
  }
`;

const RightContainersWrapper = styled(Container)`
  display: flex;
  height: 100%;
  flex-direction: column;
  width: 50%;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 100%;
  }
`;

interface Props {
  addressesList: AddressDTO[];
  employeeName: string;
  phoneNumber: string;
  workPhoneNumber: string;
  email: string;
  workEmail: string;
  handleEditAddress: (address: AddressDTO | null) => void;
}

const getAddressPurposeDescription = (purpose: number): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Identity card";
    case AddressPurpose.ForContact:
      return "For contact";
    case AddressPurpose.ForRemoteWork:
      return "For remote work";
    case AddressPurpose.Abroad:
      return "Abroad";
    case AddressPurpose.Custom:
      return "Custom";
    default:
      return "Unknown";
  }
};

const getAddressPurposeDescriptionGroupBox = (purpose: number): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Address identity card";
    case AddressPurpose.ForContact:
      return "Address for contact";
    case AddressPurpose.ForRemoteWork:
      return "Address for remote work";
    case AddressPurpose.Abroad:
      return "Address abroad";
    case AddressPurpose.Custom:
      return "Address custom";
    default:
      return "Address unknown";
  }
};

function formatFullAddress(address?: AddressDTO | null) {
  if (!address) return "";
  const parts = [];
  if (address.neighborhood) parts.push(`кв.${address.neighborhood}`);
  if (address.street) parts.push(`, ул.${address.street}`);
  if (address.block) parts.push(`, бл.${address.block}`);
  if (address.apartment) parts.push(`, ап.${address.apartment}`);
  return parts.join(" ");
}

const Addresses = ({
  addressesList,
  employeeName,
  handleEditAddress,
}: Props) => {
  const [addresses, setAddresses] = useState<AddressDTO[] | null>();

  const [selectedAddress, setSelectedAddress] = useState<AddressDTO | null>();

  useEffect(() => {
    var filteredAddresses = addressesList.filter(
      (address) => address.purpose !== AddressPurpose.IdentityCard
    );
    setAddresses(filteredAddresses);
    setSelectedAddress(filteredAddresses[0]);
  }, [addressesList]);

  const handleAddressSelect = (address: AddressDTO) => {
    setSelectedAddress(address);
  };

  const handleAddNewAddress = () => {
    setSelectedAddress(null);
  };

  return (
    <WrapperContainer>
      <ContainersWrapper>
        <MainContentWrapper>
          <TopContainer>
            <EmployeeImage
              src={profileMan}
              data-testid="profile-employee-image"
            />
            <EmployeeInfoContainer>
              <EmployeeName data-testid="profile-employee-name">
                {employeeName}
              </EmployeeName>
              <DepartmentName data-testid="profile-department-name">
                Department Name
              </DepartmentName>
            </EmployeeInfoContainer>
          </TopContainer>
          <MainContent>
            <LeftContainer data-testid="profile-left-container">
              <StyledFieldset
                onSubmit={(e) => e.preventDefault()}
                data-testid="current-address-fieldset"
              >
                <Legend data-testid="information-legend">
                  <Translator
                    getString={getAddressPurposeDescriptionGroupBox(
                      selectedAddress?.purpose ?? 1
                    )}
                  />
                </Legend>
                <EditButton
                  data-testid="edit-address-button"
                  normalImage={editIcon}
                  hoverImage={editIconHover}
                  onClick={() => handleEditAddress(selectedAddress)}
                  label=""
                  isdisabled={true}
                />
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>City</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>{selectedAddress?.city.name}</ValueLabel>
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>PC</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>{selectedAddress?.postalCode}</ValueLabel>
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>District</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>{selectedAddress?.district.name}</ValueLabel>
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>Municipality</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {selectedAddress?.municipality.name}
                    </ValueLabel>
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>Address</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {formatFullAddress(selectedAddress)}
                    </ValueLabel>
                  </ValueColumn>
                </FieldsetRow>
              </StyledFieldset>
            </LeftContainer>
            <RightContainersWrapper>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">Phone number</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    normalImage={editIcon}
                    hoverImage={editIconHover}
                    onClick={() => handleEditAddress(selectedAddress)}
                    label=""
                    isdisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>{selectedAddress?.phone}</ValueLabel>
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>{selectedAddress?.workPhone}</ValueLabel>
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">E-mail</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    normalImage={editIcon}
                    hoverImage={editIconHover}
                    onClick={() => handleEditAddress(selectedAddress)}
                    label=""
                    isdisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>{selectedAddress?.email}</ValueLabel>
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>{selectedAddress?.workEmail}</ValueLabel>
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
            </RightContainersWrapper>
          </MainContent>
        </MainContentWrapper>
        <RightColumn>
          {addresses &&
            addresses.map((address, index) => (
              <MenuItem
                key={index}
                onClick={() => handleAddressSelect(address)}
              >
                <MenuText isSelected={selectedAddress === address}>
                  {getAddressPurposeDescription(address.purpose)}
                </MenuText>
                <MenuLine />
              </MenuItem>
            ))}
          <MenuItem onClick={() => handleAddNewAddress()}>
            <MenuText>New address</MenuText>
          </MenuItem>
        </RightColumn>
      </ContainersWrapper>
    </WrapperContainer>
  );
};

export default Addresses;
