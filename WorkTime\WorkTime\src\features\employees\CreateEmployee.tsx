import { ChangeEvent, MouseEvent, useContext, useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../app/hooks";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import EmailBox from "../../components/Inputs/EmailBox";
import Label from "../../components/Inputs/Label";
import Textbox from "../../components/Inputs/Textbox";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";
import { CompanyContext } from "../companies/CompanyContext";
import { onEmployeeSaved } from "./employeesActions";

const CreateEmployeeContainer = styled(Container)`
  height: 100%;
  margin: 0 auto;
  width: clamp(40%, 30rem, 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const CreateEmployee = () => {
  const dispatch = useAppDispatch();

  const { company } = useContext(CompanyContext);
  const [employee, setEmployee] = useState({} as EmployeeDTO);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmployee({ ...employee, [e.currentTarget.name]: e.currentTarget.value });
  };

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmployee({ ...employee, email: e.currentTarget.value.trim() });
  };

  const onAddClicked = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    dispatch(
      onEmployeeSaved({
        companyId: company.id ?? "",
        employeeDTO: { ...employee, hasSignedIn: false },
      })
    );
  };

  return (
    <CreateEmployeeContainer data-testid="create-employee-container">
      <Label data-testid="add-employee-label">Add Employee</Label>
      <Textbox
        data-testid="first-name-input"
        handleChange={handleChange}
        label="Name"
        name="firstName"
        value={employee.firstName}
      />
      <Textbox
        data-testid="second-name-input"
        handleChange={handleChange}
        label="Second name"
        name="secondName"
        value={employee.secondName}
      />
      <Textbox
        data-testid="last-name-input"
        handleChange={handleChange}
        label="Last name"
        name="lastName"
        value={employee.lastName}
      />
      <Textbox
        data-testid="egn-input"
        handleChange={handleChange}
        label="EGN"
        name="egn"
        value={employee.egn}
      />
      <EmailBox
        data-testid="email-input"
        handleChange={handleEmailChange}
        label="Email"
        name="email"
        value={employee.email}
      />
      <Button
        data-testid="add-employee-button"
        label="Add Employee"
        onClick={onAddClicked}
      />
    </CreateEmployeeContainer>
  );
};

export default CreateEmployee;
