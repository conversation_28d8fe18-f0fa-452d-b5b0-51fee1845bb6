import React, { useState, useEffect } from "react";
import styled from "styled-components";
import Container from "../../components/Container";
import Avatar from "../../components/CalendarComponent/Avatar";
import Label from "../../components/Inputs/Label";
import { generateColorFromName } from "../../utils/colorUtils";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";
import { Employee } from "./useFilteredEmployees";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";

const AbsencesPageEmployeesListContainer = styled(Container)`
  display: block;
`;

const TableContainer = styled(Container)`
  background-color: var(--attendancies-right-view-table-background-color);
  border-radius: 1.8rem;
  padding-bottom: 1rem;
  overflow-y: auto;
`;

const TableHeaderRow = styled(Container)`
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
`;

const CardContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  height: 15rem;
  overflow-y: scroll;
  scrollbar-color: var(--attendancies-hover-emploeeys)
    var(--attendancies-right-view-table-background-color);
`;

const TableRow = styled(Container)<{ isSelected: boolean; isHovered: boolean }>`
  display: grid;
  grid-template-columns: 1.8rem 16rem 6rem;
  align-items: center;
  cursor: pointer;
  padding-left: 0.3rem;
  flex-shrink: 0;
  height: 1.5rem;
  column-gap: 0.2rem;
  margin: 0.1rem 0rem;
  padding-left: 0.5rem;
  background-color: ${(props) =>
    props.isSelected
      ? "var(--attendancies-selected-emploee)"
      : "var(--attendancies-right-view-table-background-color)"};

  background-color: ${(props) =>
    props.isHovered
      ? "var(--attendancies-hover-emploeeys)"
      : "var(--attendancies-right-view-table-background-color)"};
`;

interface LabelProps {
  boldness?: number;
  fontSize?: number;
  color?: string;
  justifyContent?: string;
  align?: string;
  onClick?: () => void;
}

const StyledAvatar = styled(Avatar)`
  margin-right: 0.5rem;
`;

const StyledLabel = styled(Label)<LabelProps>`
  text-align: ${(props) => props.align || "center"};
  color: ${(props) => props.color || "inherit"};
  font-weight: ${(props) => props.boldness || "normal"};
  font-size: ${(props) => (props.fontSize ? `${props.fontSize}px` : "inherit")};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

interface ExtendedEmployee extends PayrollDTO {
  isSelected: boolean;
  isHovered: boolean;
}

interface AbsencesPageEmployeesListProps {
  selectedEmployee: Employee | null;
  onSelectEmployee: (employee: Employee | null) => void;
}

const TabLabel = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: var(--attendancies-right-view-active-tab-label-font-color);
`;

export const AbsencesPageEmployeesList: React.FC<
  AbsencesPageEmployeesListProps
> = ({ selectedEmployee, onSelectEmployee }) => {
  const [extendedEmployees, setExtendedEmployees] = useState<
    ExtendedEmployee[]
  >([]);

  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls).payrolls;

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    if (!payrolls || payrolls.length === 0) {
      dispatch(onPayrollsLoaded(companyId));
    }
  }, [dispatch, payrolls]);

  useEffect(() => {
    const initializedEmployees = payrolls.map((payroll) => ({
      ...payroll,
      firstName: payroll.employee.firstName,
      midleName: payroll.employee.secondName,
      lastName: payroll.employee.lastName,
      position: payroll.position,
      isSelected: false,
      isHovered: false,
    }));
    setExtendedEmployees(initializedEmployees);
  }, [payrolls]);

  const handleRowSelection = (id: string) => {
    setExtendedEmployees((prevEmployees) =>
      prevEmployees.map((employee) =>
        employee.workTimeId === id
          ? { ...employee, isSelected: !employee.isSelected }
          : { ...employee, isSelected: false }
      )
    );

    const selectedEmp = extendedEmployees.find((emp) => emp.workTimeId === id);
    if (selectedEmp && selectedEmp.isSelected) {
      onSelectEmployee(null);
    } else if (selectedEmp) {
      const employeeData: Employee = {
        payrollId: selectedEmp.workTimeId,
        name: `${selectedEmp.employee.firstName} ${selectedEmp.employee.lastName}`,
        leaveStart: "",
        leaveEnd: "",
        color: generateColorFromName(
          `${selectedEmp.employee.firstName} ${selectedEmp.employee.lastName}`
        ),
      };
      onSelectEmployee(employeeData);
    }
  };

  const handleEmployeeHover = (id: string | null) => {
    setExtendedEmployees((prevEmployees) =>
      prevEmployees.map((employee) => ({
        ...employee,
        isHovered: employee.workTimeId === id,
      }))
    );
  };

  return (
    <AbsencesPageEmployeesListContainer data-testid="absences-page-employees-list-container">
      <TableContainer data-testid="table-container">
        <TableHeaderRow data-testid="table-header-row">
          <TabLabel children="DEPARTMENT" />
        </TableHeaderRow>
        <CardContainer>
          {extendedEmployees.map((employee: ExtendedEmployee) => (
            <TableRow
              key={employee.workTimeId}
              isSelected={employee.isSelected}
              isHovered={employee.isHovered}
              onClick={() => handleRowSelection(employee.workTimeId)}
              onMouseEnter={() => handleEmployeeHover(employee.workTimeId)}
              onMouseLeave={() => handleEmployeeHover(null)}
              data-testid={`table-row-${employee.workTimeId}`}
            >
              <StyledAvatar
                name={`${employee.employee.firstName} ${employee.employee.lastName}`}
                photo={""}
                size={1}
                isVisible={true}
                background={generateColorFromName(
                  `${employee.employee.firstName} ${employee.employee.lastName}`
                )}
                data-testid={`styled-avatar-${employee.workTimeId}`}
              />
              <StyledLabel
                children={`${employee.employee.firstName} ${employee.employee.lastName}`}
                boldness={450}
                fontSize={15}
                align="left"
                data-testid={`styled-label-${employee.workTimeId}`}
              />
              <StyledLabel
                children={`${employee.position.name} `}
                boldness={450}
                fontSize={15}
                align="left"
                color="var(--attendancies-right-view-position-font-color)"
                data-testid={`styled-label-position-${employee.workTimeId}`}
              ></StyledLabel>
            </TableRow>
          ))}
        </CardContainer>
      </TableContainer>
    </AbsencesPageEmployeesListContainer>
  );
};

export default AbsencesPageEmployeesList;
