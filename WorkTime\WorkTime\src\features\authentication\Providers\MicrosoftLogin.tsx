import React, { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "../AuthContext";
import * as msal from "@azure/msal-browser";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import MicrosoftLogo from "../../../assets/images/logos/microsoft-logo.png";
import { microsoftLogin } from "../../../services/authentication/authenticationService";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../../constants/local-storage-constants";
import { getWorkTimeRole } from "../../../services/authorization/authorizationService";

interface Props {
  returnAfterLogin?: string;
}

const MicrosoftLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useContext(AuthContext);

  const [msalInstance, setMsalInstance] =
    useState<msal.PublicClientApplication | null>(null);

  const msalConfig = {
    auth: {
      clientId: import.meta.env.VITE_MICROSOFT_ID ?? "",
    },
  };

  useEffect(() => {
    const instance = new msal.PublicClientApplication(msalConfig);
    setMsalInstance(instance);
    instance.initialize();
  }, []);

  const login = () => {
    msalInstance
      ?.acquireTokenPopup({
        scopes: ["User.Read", "email"],
      })
      .then((response) => {
        microsoftLogin(response.accessToken).then(async (email) => {
          if (email) {
            await getWorkTimeRole();

            setUser({
              email: email,
              hasSignedIn: true,
              workTimeRoleName:
                localStorage.getItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME) ?? "",
            });
            navigate(returnAfterLogin ?? "/");
          }
        });
      });
  };

  return (
    <OAuthButton
      logo={MicrosoftLogo}
      content="Sign In with Microsoft account"
      onClick={login}
      data-testid="microsoft-login-button"
    />
  );
};

export default MicrosoftLogin;
