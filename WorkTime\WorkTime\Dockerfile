# Stage 1
FROM node:14-alpine as build
WORKDIR /worktime
ARG VITE_GATEWAY_API
ENV VITE_GATEWAY_API $VITE_GATEWAY_API
COPY package.json /worktime/package.json
RUN npm install --silent
COPY . /worktime
RUN npm install jest
RUN npm install vite
RUN npm run build --verbose

# Stage 2
FROM nginx:1.25.2-alpine
COPY --from=build /worktime/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
CMD ["nginx", "-g", "daemon off;"]