import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import {
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
} from "../../services/connectionService";
import { Action, Reducer } from "redux";
import { JoinCompanyRequest } from "../../models/Requests/Companies/JoinCompanyRequest";
import { onImportSenderaCompany } from "./senderaCompaniesAction";
import { toast } from "react-toastify";
import { translate } from "../../services/language/Translator";
import { GetCompaniesResponse } from "../../models/Responses/GetCompaniesResponse";

export enum Status {
  Default,
  Fetched,
  Fetching,
}

interface CompaniesState {
  activeCompanies: CompanyDTO[];
  pendingCompanies: CompanyDTO[];
  status: Status;
}

interface GetCompaniesAction {
  type: "GET_COMPANIES";
  companies: GetCompaniesResponse;
}
interface JoinCompanyAction {
  type: "JOIN_COMPANY";
  company: CompanyDTO;
}

interface CreateCompanyAction {
  type: "CREATE_COMPANY";
  company: CompanyDTO;
}

interface EditCompanyAction {
  type: "EDIT_COMPANY";
  company: CompanyDTO;
}

interface SetCompanyStatusAction {
  type: "SET_COMPANY_STATUS";
  status: Status;
}

interface AcceptCompanyAction {
  type: "ACCEPT_COMPANY";
  company: CompanyDTO;
}

interface DeclineCompanyAction {
  type: "DECLINE_COMPANY";
  company: CompanyDTO;
}

type KnownActions =
  | GetCompaniesAction
  | JoinCompanyAction
  | CreateCompanyAction
  | EditCompanyAction
  | SetCompanyStatusAction
  | AcceptCompanyAction
  | DeclineCompanyAction
  | ClearStateAction;

const getCompaniesAction = (
  companies: GetCompaniesResponse
): GetCompaniesAction => ({
  type: "GET_COMPANIES",
  companies,
});

const joinCompanyAction = (company: CompanyDTO): JoinCompanyAction => ({
  type: "JOIN_COMPANY",
  company,
});

const createCompanyAction = (company: CompanyDTO): CreateCompanyAction => ({
  type: "CREATE_COMPANY",
  company: company,
});

const editCompanyAction = (company: CompanyDTO): EditCompanyAction => ({
  type: "EDIT_COMPANY",
  company,
});

const setCompanyStatusAction = (status: Status): SetCompanyStatusAction => ({
  type: "SET_COMPANY_STATUS",
  status,
});

const acceptCompanyAction = (company: CompanyDTO): AcceptCompanyAction => ({
  type: "ACCEPT_COMPANY",
  company,
});

const declineCompanyAction = (company: CompanyDTO): DeclineCompanyAction => ({
  type: "DECLINE_COMPANY",
  company,
});

export const actionCreators = {
  onGetCompanies: (): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(onSetCompanyStatus(Status.Fetching));

      authenticatedGet<GetCompaniesResponse>(`companies`)
        .then((companies) => {
          dispatch(getCompaniesAction(companies));
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(dispatch(onSetCompanyStatus(Status.Fetched)));
    };
  },
  onJoinCompany: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const joinCompanyRequest = {
        bulstat: company.bulstat,
      } as JoinCompanyRequest;
      authenticatedPost<CompanyDTO>("join-company", joinCompanyRequest)
        .then((company) => {
          dispatch(joinCompanyAction(company));
        })
        .catch((error) => {
          console.error(error);
        });
    };
  },
  onCompanyImported: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(joinCompanyAction(company));
      dispatch(onImportSenderaCompany(company));
    };
  },
  onCompanyCreated: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(createCompanyAction(company));
      toast.success(translate("strCompanyAddedSuccessfully"));
    };
  },
  onCompanyEdit: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedPut<CompanyDTO>(`company`, company).then((editedCompany) => {
        dispatch(editCompanyAction(editedCompany));
      });
    };
  },
  onSetCompanyStatus: (status: Status): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(setCompanyStatusAction(status));
    };
  },
  onAcceptCompany: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(acceptCompanyAction(company));
    };
  },
  onDeclineCompany: (company: CompanyDTO): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(declineCompanyAction(company));
    };
  },
};

export const {
  onGetCompanies,
  onJoinCompany,
  onCompanyImported,
  onCompanyCreated,
  onCompanyEdit,
  onSetCompanyStatus,
  onAcceptCompany,
  onDeclineCompany,
} = actionCreators;

const initialState = {
  activeCompanies: [],
  pendingCompanies: [],
  status: Status.Fetched,
} as CompaniesState;

export const reducer: Reducer<CompaniesState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "GET_COMPANIES":
      return {
        ...state,
        activeCompanies: [...incomingAction.companies.activeCompanies],
        pendingCompanies: [...incomingAction.companies.pendingCompanies],
      };
    case "CREATE_COMPANY":
    case "JOIN_COMPANY":
      return {
        ...state,
        activeCompanies: [...state.activeCompanies, incomingAction.company],
      };
    case "EDIT_COMPANY":
      var editedCompany = incomingAction.company;
      const originalIndex = state.activeCompanies.findIndex(
        (comp) => comp.id === editedCompany.id
      );
      const newActiveCompanies = [...state.activeCompanies];
      newActiveCompanies.splice(originalIndex, 1, editedCompany);
      return {
        ...state,
        activeCompanies: newActiveCompanies,
      };
    case "SET_COMPANY_STATUS":
      return {
        ...state,
        status: incomingAction.status,
      };
    case "ACCEPT_COMPANY":
      var acceptedCompany = incomingAction.company;
      return {
        ...state,
        pendingCompanies: state.pendingCompanies.filter(
          (comp) => comp.id !== acceptedCompany.id
        ),
        activeCompanies: [...state.activeCompanies, acceptedCompany],
      };
    case "DECLINE_COMPANY":
      var declinedCompany = incomingAction.company;
      return {
        ...state,
        pendingCompanies: [
          ...state.pendingCompanies.filter(
            (comp) => comp.id !== declinedCompany.id
          ),
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const companiesState = (state: RootState) => state.companies;
