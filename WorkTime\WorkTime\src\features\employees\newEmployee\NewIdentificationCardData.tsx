import { useEffect, useState } from "react";
import styled from "styled-components";
import Container from "../../../components/Container";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1rem;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  background-color: #45b6f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: #333;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const DropdownContainer = styled(Container)`
  position: relative;
  flex: 1;
`;

const DropdownHeader = styled.div`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1.25rem 1.25rem 0.6rem;
  margin: 0.2rem 0 0.2rem 0;
  outline: none;
  color: var(--input-field-color);
  background: var(--input-field-background-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`;

const DropdownLabel = styled.label<{ hasValue: boolean }>`
  position: absolute;
  left: 1.25rem;
  transition: all 0.2s ease;
  pointer-events: none;
  color: var(--input-field-label-color);
  font-size: ${(props) => (props.hasValue ? "0.75rem" : "1rem")};
  top: ${(props) => (props.hasValue ? "0.5rem" : "1.25rem")};
`;

const DropdownArrow = styled.div<{ isOpen: boolean }>`
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  transition: transform 0.2s ease;
`;

const DropdownBody = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: ${(props) => (props.isOpen ? "block" : "none")};
  max-height: 200px;
  overflow-y: auto;
`;

const DropdownOption = styled.div`
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #eee;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: any;
}

interface FormData {
  idNumber: string;
  issuedOn: string;
  issuedBy: string;
  citizenship: string;
  gender: string;
  city: string;
  postalCode: string;
  region: string;
  municipality: string;
  quarter: string;
  street: string;
  block: string;
  apartment: string;
}

const NewIdentificationCardData = ({ onValidation, data = {} }: Props) => {
  const [formData, setFormData] = useState<FormData>({
    idNumber: data.idNumber || "",
    issuedOn: data.issuedOn || "",
    issuedBy: data.issuedBy || "",
    citizenship: data.citizenship || "",
    gender: data.gender || "",
    city: data.city || "",
    postalCode: data.postalCode || "",
    region: data.region || "",
    municipality: data.municipality || "",
    quarter: data.quarter || "",
    street: data.street || "",
    block: data.block || "",
    apartment: data.apartment || "",
  });

  const [isGenderDropdownOpen, setIsGenderDropdownOpen] = useState(false);

  const genderOptions = [
    { value: "male", label: "Мъж" },
    { value: "female", label: "Жена" },
  ];

  useEffect(() => {
    const isValid =
      formData.idNumber.trim() !== "" &&
      formData.issuedOn.trim() !== "" &&
      formData.issuedBy.trim() !== "" &&
      formData.citizenship.trim() !== "" &&
      formData.gender.trim() !== "";

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: FormData) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleGenderSelect = (value: string) => {
    setFormData((prev: FormData) => ({
      ...prev,
      gender: value,
    }));
    setIsGenderDropdownOpen(false);
  };

  const getGenderLabel = () => {
    const option = genderOptions.find((opt) => opt.value === formData.gender);
    return option ? option.label : "";
  };

  return (
    <FormContainer>
      {/* Personal Card Section */}
      <SectionHeader>
        <SectionIcon>🆔</SectionIcon>
        <SectionTitle>Лична карта</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="idNumber"
            label="ЛК Номер"
            value={formData.idNumber}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="issuedOn"
            label="Издадена на"
            value={formData.issuedOn}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="issuedBy"
            label="Издадена от"
            value={formData.issuedBy}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="citizenship"
            label="Гражданство"
            value={formData.citizenship}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <DropdownContainer>
            <DropdownHeader
              onClick={() => setIsGenderDropdownOpen(!isGenderDropdownOpen)}
            >
              <DropdownLabel hasValue={!!formData.gender}>Пол</DropdownLabel>
              <span style={{ marginTop: formData.gender ? "0.5rem" : "0" }}>
                {getGenderLabel()}
              </span>
              <DropdownArrow isOpen={isGenderDropdownOpen} />
            </DropdownHeader>
            <DropdownBody isOpen={isGenderDropdownOpen}>
              {genderOptions.map((option) => (
                <DropdownOption
                  key={option.value}
                  onClick={() => handleGenderSelect(option.value)}
                >
                  {option.label}
                </DropdownOption>
              ))}
            </DropdownBody>
          </DropdownContainer>
        </FormField>
        <FormField>{/* Empty field to maintain layout */}</FormField>
      </FormRow>

      {/* Address per ID Card Section */}
      <SectionHeader>
        <SectionIcon>📍</SectionIcon>
        <SectionTitle>Адрес по ЛК</SectionTitle>
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="city"
            label="Град"
            value={formData.city}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="postalCode"
            label="ПК"
            value={formData.postalCode}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="region"
            label="Област"
            value={formData.region}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="municipality"
            label="Община"
            value={formData.municipality}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="quarter"
            label="Квартал"
            value={formData.quarter}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="street"
            label="Улица"
            value={formData.street}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="block"
            label="Блок"
            value={formData.block}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="apartment"
            label="Апартament"
            value={formData.apartment}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>
    </FormContainer>
  );
};

export default NewIdentificationCardData;
