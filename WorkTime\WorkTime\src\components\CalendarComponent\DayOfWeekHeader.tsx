import React from "react";
import styled from "styled-components";
import { daysOfWeek } from "./constants/Names";
import Container from "../Container";
import Label from "../Inputs/Label";

const Row = styled(Container)`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  justify-content: center;
  width: 100%;
`;

const DayName = styled(Label)`
  padding: 0.4em;
  font-size: clamp(0.95em, 1.5vw, 1.5em);
  color: var(--datepicker-name-of-days-color);
  height: 1.5em;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
`;

export const DayOfWeekHeader: React.FC = () => {
  return (
    <Row data-testid="day-of-week-header">
      {daysOfWeek.map((day, index) => (
        <DayName key={index} data-testid={`day-name-${day.toLowerCase()}`}>
          {day}
        </DayName>
      ))}
    </Row>
  );
};
